<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$streamflix = new StreamFlix();

// Handle TMDB import
if ($_POST['action'] ?? '' === 'import_content') {
    $tmdb_id = (int)($_POST['tmdb_id'] ?? 0);
    $content_type = sanitizeInput($_POST['content_type'] ?? 'movie');
    
    if ($tmdb_id > 0) {
        if ($content_type === 'movie') {
            $result = $streamflix->importMovie($tmdb_id);
        } else {
            $result = $streamflix->importTVShow($tmdb_id);
        }
        
        $message = $result ? 'Content imported successfully!' : 'Failed to import content.';
    }
}

// Get statistics
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) as count FROM movies");
    $stats['movies'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows");
    $stats['tv_shows'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
    $stats['users'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // Get server statistics
    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers");
    $stats['servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

    $stmt = $conn->query("SELECT COUNT(*) as count FROM embed_servers WHERE is_active = 1");
    $stats['active_servers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

} catch (Exception $e) {
    $stats = ['movies' => 0, 'tv_shows' => 0, 'users' => 0, 'servers' => 0, 'active_servers' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .admin-header {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .import-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            flex: 1;
        }
        
        .success-message {
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .error-message {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .admin-nav {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .admin-nav a {
            padding: 10px 20px;
            background: var(--border-color);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Admin Panel</h1>
            <p>Manage your streaming platform</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php" class="active">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php">Maintenance</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="<?php echo $result ? 'success' : 'error'; ?>-message">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['movies']); ?></div>
                <div>Movies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['tv_shows']); ?></div>
                <div>TV Shows</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['users']); ?></div>
                <div>Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['servers']); ?></div>
                <div>Total Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['active_servers']); ?></div>
                <div>Active Servers</div>
            </div>
        </div>

        <!-- Import Content -->
        <div class="import-section">
            <h2>Import Content from TMDB</h2>
            <p>Enter TMDB ID to import movies or TV shows automatically</p>
            
            <form method="POST" action="">
                <input type="hidden" name="action" value="import_content">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="tmdb_id">TMDB ID</label>
                        <input type="number" id="tmdb_id" name="tmdb_id" required 
                               placeholder="e.g., 385687 for Fast X">
                    </div>
                    
                    <div class="form-group">
                        <label for="content_type">Type</label>
                        <select id="content_type" name="content_type" required>
                            <option value="movie">Movie</option>
                            <option value="tv_show">TV Show</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Import</button>
                    </div>
                </div>
            </form>
            
            <div style="margin-top: 20px; padding: 15px; background: var(--dark-bg); border-radius: 4px;">
                <h4>How to find TMDB ID:</h4>
                <ol>
                    <li>Go to <a href="https://www.themoviedb.org" target="_blank" style="color: var(--primary-color);">themoviedb.org</a></li>
                    <li>Search for the movie or TV show</li>
                    <li>The ID is in the URL: themoviedb.org/movie/<strong>385687</strong>-fast-x</li>
                </ol>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="import-section">
            <h2>Quick Actions</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn btn-secondary" onclick="importTrending('movie')">Import Trending Movies</button>
                <button class="btn btn-secondary" onclick="importTrending('tv')">Import Trending TV Shows</button>
                <button class="btn btn-secondary" onclick="importPopular('movie')">Import Popular Movies</button>
                <button class="btn btn-secondary" onclick="importPopular('tv')">Import Popular TV Shows</button>
                <button class="btn btn-secondary" onclick="importNowPlaying()">Import Now Playing</button>
                <button class="btn btn-secondary" onclick="importUpcoming()">Import Upcoming Movies</button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="import-section">
            <h2>Recent Activity</h2>
            <div id="recentActivity">
                <p style="color: var(--text-secondary);">Loading recent activity...</p>
            </div>
        </div>
    </div>

    <script>
        async function importTrending(type) {
            if (!confirm(`Import trending ${type === 'movie' ? 'movies' : 'TV shows'}? This may take a while.`)) {
                return;
            }
            
            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_trending',
                        type: type
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Successfully imported ${data.count} ${type === 'movie' ? 'movies' : 'TV shows'}`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }
        
        async function importPopular(type) {
            if (!confirm(`Import popular ${type === 'movie' ? 'movies' : 'TV shows'}? This may take a while.`)) {
                return;
            }

            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_popular',
                        type: type
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully imported ${data.count} ${type === 'movie' ? 'movies' : 'TV shows'}`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }

        async function importNowPlaying() {
            if (!confirm('Import now playing movies? This may take a while.')) {
                return;
            }

            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_now_playing',
                        type: 'movie'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully imported ${data.count} movies`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }

        async function importUpcoming() {
            if (!confirm('Import upcoming movies? This may take a while.')) {
                return;
            }

            try {
                const response = await fetch('api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_upcoming',
                        type: 'movie'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully imported ${data.count} movies`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }

        // Load recent activity
        async function loadRecentActivity() {
            try {
                const response = await fetch('api/recent-activity.php');
                const data = await response.json();

                if (data.success) {
                    const activityDiv = document.getElementById('recentActivity');
                    if (data.activities.length > 0) {
                        activityDiv.innerHTML = data.activities.map(activity => `
                            <div style="padding: 10px; border-bottom: 1px solid var(--border-color); color: var(--text-secondary);">
                                <strong style="color: var(--text-primary);">${activity.type}</strong>: ${activity.title}
                                <small style="float: right;">${activity.date}</small>
                            </div>
                        `).join('');
                    } else {
                        activityDiv.innerHTML = '<p style="color: var(--text-secondary);">No recent activity</p>';
                    }
                }
            } catch (error) {
                console.error('Failed to load recent activity:', error);
            }
        }

        // Load recent activity on page load
        document.addEventListener('DOMContentLoaded', loadRecentActivity);
    </script>
</body>
</html>
