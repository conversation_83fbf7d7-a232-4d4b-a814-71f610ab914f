<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Handle actions
if ($_POST['action'] ?? '' === 'toggle_status') {
    $user_id = (int)($_POST['user_id'] ?? 0);
    if ($user_id > 0 && $user_id != $_SESSION['user_id']) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("UPDATE users SET is_active = NOT is_active WHERE id = :id");
            $stmt->execute([':id' => $user_id]);
            $message = 'User status updated!';
        } catch (Exception $e) {
            $error = 'Failed to update user: ' . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'change_role') {
    $user_id = (int)($_POST['user_id'] ?? 0);
    $new_role = sanitizeInput($_POST['new_role'] ?? '');
    
    if ($user_id > 0 && $user_id != $_SESSION['user_id'] && in_array($new_role, ['user', 'admin'])) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("UPDATE users SET role = :role WHERE id = :id");
            $stmt->execute([':role' => $new_role, ':id' => $user_id]);
            $message = 'User role updated!';
        } catch (Exception $e) {
            $error = 'Failed to update user role: ' . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'delete_user') {
    $user_id = (int)($_POST['user_id'] ?? 0);
    if ($user_id > 0 && $user_id != $_SESSION['user_id']) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("DELETE FROM users WHERE id = :id");
            $stmt->execute([':id' => $user_id]);
            $message = 'User deleted successfully!';
        } catch (Exception $e) {
            $error = 'Failed to delete user: ' . $e->getMessage();
        }
    }
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get users with pagination
    $stmt = $conn->prepare("
        SELECT u.*, COUNT(*) OVER() as total_count,
               (SELECT COUNT(*) FROM watchlist w WHERE w.user_id = u.id) as watchlist_count
        FROM users u 
        ORDER BY u.created_at DESC 
        LIMIT :limit OFFSET :offset
    ");
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_count = $users[0]['total_count'] ?? 0;
    $total_pages = ceil($total_count / $limit);
    
    // Get statistics
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users");
    $stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total_users'];
    
    $stmt = $conn->query("SELECT COUNT(*) as active_users FROM users WHERE is_active = 1");
    $stats['active_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['active_users'];
    
    $stmt = $conn->query("SELECT COUNT(*) as admin_users FROM users WHERE role = 'admin'");
    $stats['admin_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['admin_users'];
    
} catch (Exception $e) {
    $users = [];
    $total_pages = 0;
    $stats = ['total_users' => 0, 'active_users' => 0, 'admin_users' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Users - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .users-table {
            background: var(--secondary-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .table th {
            background: var(--dark-bg);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .table td {
            color: var(--text-secondary);
        }
        
        .user-info {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-active {
            background: #28a745;
            color: white;
        }
        
        .badge-inactive {
            background: #dc3545;
            color: white;
        }
        
        .badge-admin {
            background: var(--primary-color);
            color: white;
        }
        
        .badge-user {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .btn-toggle {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .btn-toggle:hover {
            background: var(--primary-color);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .role-select {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: var(--primary-color);
        }
        
        .pagination .current {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Manage Users</h1>
            <p>View and manage all users in your system</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php" class="active">Users</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_users']); ?></div>
                <div>Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['active_users']); ?></div>
                <div>Active Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['admin_users']); ?></div>
                <div>Admin Users</div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="users-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Watchlist</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <div class="user-info"><?php echo htmlspecialchars($user['username']); ?></div>
                            <small>ID: <?php echo $user['id']; ?></small>
                        </td>
                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                        <td>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="change_role">
                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                <select name="new_role" class="role-select" onchange="this.form.submit()" 
                                        <?php echo $user['id'] == $_SESSION['user_id'] ? 'disabled' : ''; ?>>
                                    <option value="user" <?php echo $user['role'] == 'user' ? 'selected' : ''; ?>>User</option>
                                    <option value="admin" <?php echo $user['role'] == 'admin' ? 'selected' : ''; ?>>Admin</option>
                                </select>
                            </form>
                        </td>
                        <td>
                            <span class="status-badge <?php echo $user['is_active'] ? 'badge-active' : 'badge-inactive'; ?>">
                                <?php echo $user['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td><?php echo $user['watchlist_count']; ?> items</td>
                        <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                        <td>
                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                    <button type="submit" class="action-btn btn-toggle">
                                        <?php echo $user['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                    </button>
                                </form>
                                
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                    <input type="hidden" name="action" value="delete_user">
                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                    <button type="submit" class="action-btn btn-delete">Delete</button>
                                </form>
                            <?php else: ?>
                                <span style="color: var(--text-secondary); font-size: 0.8rem;">Current User</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>">← Previous</a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">Next →</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
