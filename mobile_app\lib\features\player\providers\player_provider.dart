import 'package:flutter/foundation.dart';
import 'package:video_player/video_player.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';

class PlayerProvider extends ChangeNotifier {
  VideoPlayerController? _videoController;
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  
  // Player state
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  
  // Content info
  int? _tmdbId;
  String? _contentType;
  int? _season;
  int? _episode;
  
  // Server and quality info
  List<Map<String, dynamic>> _availableServers = [];
  Map<String, dynamic>? _currentServer;
  List<String> _availableQualities = ['Auto', '1080p', '720p', '480p'];
  String _currentQuality = 'Auto';
  List<String> _availableSubtitles = ['None', 'English', 'Spanish'];
  String _currentSubtitle = 'None';

  // Getters
  VideoPlayerController? get videoController => _videoController;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  bool get isPlaying => _isPlaying;
  Duration get position => _position;
  Duration get duration => _duration;
  double get volume => _volume;
  double get playbackSpeed => _playbackSpeed;
  List<Map<String, dynamic>> get availableServers => _availableServers;
  Map<String, dynamic>? get currentServer => _currentServer;
  List<String> get availableQualities => _availableQualities;
  String get currentQuality => _currentQuality;
  List<String> get availableSubtitles => _availableSubtitles;
  String get currentSubtitle => _currentSubtitle;

  // Initialize player
  Future<void> initializePlayer({
    required int tmdbId,
    required String contentType,
    int? season,
    int? episode,
  }) async {
    _tmdbId = tmdbId;
    _contentType = contentType;
    _season = season;
    _episode = episode;
    
    _setLoading(true);
    _setError(false);
    
    try {
      await _loadEmbedUrls();
      if (_availableServers.isNotEmpty) {
        _currentServer = _availableServers.first;
        await _initializeVideoPlayer();
      } else {
        _setError(true, 'No streaming servers available');
      }
    } catch (e) {
      _setError(true, e.toString());
    }
    
    _setLoading(false);
  }

  // Load embed URLs from API
  Future<void> _loadEmbedUrls() async {
    try {
      final response = await ApiService.getEmbedUrls(
        _contentType!,
        _tmdbId!,
        season: _season,
        episode: _episode,
      );
      
      if (response.success && response.data != null) {
        _availableServers = List<Map<String, dynamic>>.from(
          response.data['servers'] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error loading embed URLs: $e');
      // Fallback to demo servers
      _availableServers = _getDemoServers();
    }
  }

  // Get demo servers for testing
  List<Map<String, dynamic>> _getDemoServers() {
    return [
      {
        'name': 'Server 1',
        'url': 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
        'quality': '1080p',
      },
      {
        'name': 'Server 2', 
        'url': 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
        'quality': '720p',
      },
    ];
  }

  // Initialize video player controller
  Future<void> _initializeVideoPlayer() async {
    if (_currentServer == null) return;
    
    try {
      await _disposeVideoController();
      
      final videoUrl = _currentServer!['url'];
      _videoController = VideoPlayerController.network(videoUrl);
      
      await _videoController!.initialize();
      
      _videoController!.addListener(_videoPlayerListener);
      
      _duration = _videoController!.value.duration;
      _isPlaying = false;
      
      notifyListeners();
      
      // Auto-play
      await play();
      
    } catch (e) {
      _setError(true, 'Failed to load video: $e');
    }
  }

  // Video player listener
  void _videoPlayerListener() {
    if (_videoController != null) {
      _position = _videoController!.value.position;
      _duration = _videoController!.value.duration;
      _isPlaying = _videoController!.value.isPlaying;
      
      if (_videoController!.value.hasError) {
        _setError(true, _videoController!.value.errorDescription ?? 'Video error');
      }
      
      notifyListeners();
    }
  }

  // Play video
  Future<void> play() async {
    if (_videoController != null) {
      await _videoController!.play();
      _addToWatchHistory();
    }
  }

  // Pause video
  Future<void> pause() async {
    if (_videoController != null) {
      await _videoController!.pause();
    }
  }

  // Toggle play/pause
  Future<void> togglePlayPause() async {
    if (_isPlaying) {
      await pause();
    } else {
      await play();
    }
  }

  // Seek to position
  Future<void> seekTo(Duration position) async {
    if (_videoController != null) {
      await _videoController!.seekTo(position);
    }
  }

  // Seek forward
  Future<void> seekForward([Duration? duration]) async {
    final seekDuration = duration ?? const Duration(seconds: 10);
    final newPosition = _position + seekDuration;
    await seekTo(newPosition);
  }

  // Seek backward
  Future<void> seekBackward([Duration? duration]) async {
    final seekDuration = duration ?? const Duration(seconds: 10);
    final newPosition = _position - seekDuration;
    await seekTo(newPosition > Duration.zero ? newPosition : Duration.zero);
  }

  // Set volume
  Future<void> setVolume(double volume) async {
    _volume = volume.clamp(0.0, 1.0);
    if (_videoController != null) {
      await _videoController!.setVolume(_volume);
    }
    notifyListeners();
  }

  // Set playback speed
  Future<void> setPlaybackSpeed(double speed) async {
    _playbackSpeed = speed;
    if (_videoController != null) {
      await _videoController!.setPlaybackSpeed(_playbackSpeed);
    }
    notifyListeners();
  }

  // Change server
  Future<void> changeServer(Map<String, dynamic> server) async {
    _currentServer = server;
    final currentPosition = _position;
    
    _setLoading(true);
    await _initializeVideoPlayer();
    
    // Resume from previous position
    if (currentPosition > Duration.zero) {
      await seekTo(currentPosition);
    }
    
    _setLoading(false);
  }

  // Change quality
  Future<void> changeQuality(String quality) async {
    _currentQuality = quality;
    
    // Find server with matching quality
    final serverWithQuality = _availableServers.firstWhere(
      (server) => server['quality'] == quality,
      orElse: () => _availableServers.first,
    );
    
    if (serverWithQuality != _currentServer) {
      await changeServer(serverWithQuality);
    }
    
    notifyListeners();
  }

  // Change subtitle
  Future<void> changeSubtitle(String subtitle) async {
    _currentSubtitle = subtitle;
    // Implement subtitle logic here
    notifyListeners();
  }

  // Retry playback
  Future<void> retryPlayback() async {
    _setError(false);
    await _initializeVideoPlayer();
  }

  // Add to watch history
  Future<void> _addToWatchHistory() async {
    if (_tmdbId != null && _contentType != null) {
      final content = {
        'tmdb_id': _tmdbId,
        'content_type': _contentType,
        'title': 'Content Title', // You might want to pass this
        'poster_path': '', // You might want to pass this
        'progress': 0.0,
        if (_season != null) 'season': _season,
        if (_episode != null) 'episode': _episode,
      };
      
      await StorageService.addToWatchHistory(content);
    }
  }

  // Update watch progress
  Future<void> updateWatchProgress() async {
    if (_duration.inMilliseconds > 0) {
      final progress = _position.inMilliseconds / _duration.inMilliseconds;
      
      // Save progress to storage
      final settings = StorageService.getSettings();
      settings['last_position_${_tmdbId}_${_contentType}'] = _position.inMilliseconds;
      await StorageService.saveSettings(settings);
    }
  }

  // Load saved progress
  Future<void> loadSavedProgress() async {
    final settings = StorageService.getSettings();
    final savedPosition = settings['last_position_${_tmdbId}_${_contentType}'];
    
    if (savedPosition != null && savedPosition > 0) {
      final position = Duration(milliseconds: savedPosition);
      await seekTo(position);
    }
  }

  // Dispose video controller
  Future<void> _disposeVideoController() async {
    if (_videoController != null) {
      _videoController!.removeListener(_videoPlayerListener);
      await _videoController!.dispose();
      _videoController = null;
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool error, [String? message]) {
    _hasError = error;
    _errorMessage = message;
    notifyListeners();
  }

  @override
  void dispose() {
    _disposeVideoController();
    super.dispose();
  }

  // Format duration
  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '$hours:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  // Get progress percentage
  double get progressPercentage {
    if (_duration.inMilliseconds == 0) return 0.0;
    return _position.inMilliseconds / _duration.inMilliseconds;
  }
}
