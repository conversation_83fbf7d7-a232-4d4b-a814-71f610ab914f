import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/device_utils.dart';
import '../providers/home_provider.dart';
import '../widgets/hero_section.dart';
import '../widgets/content_section.dart';
import '../widgets/content_card.dart';
import '../../player/screens/player_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin {
  late ScrollController _scrollController;
  bool _showAppBar = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeProvider>().loadHomeData();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final showAppBar = _scrollController.offset > 200;
    if (showAppBar != _showAppBar) {
      setState(() {
        _showAppBar = showAppBar;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppTheme.darkBg,
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: Consumer<HomeProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.featuredMovies.isEmpty) {
            return _buildLoadingState();
          }

          if (provider.hasError && provider.featuredMovies.isEmpty) {
            return _buildErrorState(provider);
          }

          return RefreshIndicator(
            onRefresh: () => provider.loadHomeData(),
            color: AppTheme.primaryColor,
            backgroundColor: AppTheme.cardBg,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // Hero Section
                if (provider.heroContent != null)
                  SliverToBoxAdapter(
                    child: HeroSection(
                      content: provider.heroContent!,
                      onPlayTap: () => _navigateToPlayer(provider.heroContent!),
                      onInfoTap: () => _showContentDetails(provider.heroContent!),
                    ),
                  ),

                // Featured Movies
                if (provider.featuredMovies.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '🎬 Featured Movies',
                      items: provider.featuredMovies,
                      onSeeAllTap: () => _navigateToMovies('featured'),
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // Trending Movies
                if (provider.trendingMovies.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '🔥 Trending Movies',
                      items: provider.trendingMovies,
                      onSeeAllTap: () => _navigateToMovies('trending'),
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // Featured TV Shows
                if (provider.featuredTvShows.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '📺 Featured TV Shows',
                      items: provider.featuredTvShows,
                      onSeeAllTap: () => _navigateToTvShows('featured'),
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // Trending TV Shows
                if (provider.trendingTvShows.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '🔥 Trending TV Shows',
                      items: provider.trendingTvShows,
                      onSeeAllTap: () => _navigateToTvShows('trending'),
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // Continue Watching
                if (provider.continueWatching.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '▶️ Continue Watching',
                      items: provider.continueWatching,
                      showProgress: true,
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // My Watchlist
                if (provider.watchlist.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '📚 My Watchlist',
                      items: provider.watchlist,
                      onSeeAllTap: () => _navigateToWatchlist(),
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // Recently Added
                if (provider.recentlyAdded.isNotEmpty)
                  SliverToBoxAdapter(
                    child: ContentSection(
                      title: '🆕 Recently Added',
                      items: provider.recentlyAdded,
                      onItemTap: _navigateToPlayer,
                    ),
                  ),

                // Bottom padding
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: _showAppBar 
          ? AppTheme.cardBg.withOpacity(0.95)
          : Colors.transparent,
      elevation: _showAppBar ? 4 : 0,
      title: AnimatedOpacity(
        opacity: _showAppBar ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.play_arrow_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'StreamFlix',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimary,
              ),
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          onPressed: () => _showNotifications(),
        ),
        IconButton(
          icon: const Icon(Icons.cast_outlined),
          onPressed: () => _showCastDialog(),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Hero loading
          Container(
            height: 400,
            color: AppTheme.cardBg,
            child: Shimmer.fromColors(
              baseColor: AppTheme.cardBg,
              highlightColor: AppTheme.borderColor,
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.cardBg,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          
          // Content sections loading
          ...List.generate(4, (index) => _buildLoadingSection()),
        ],
      ),
    );
  }

  Widget _buildLoadingSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: AppTheme.cardBg,
            highlightColor: AppTheme.borderColor,
            child: Container(
              width: 200,
              height: 24,
              decoration: BoxDecoration(
                color: AppTheme.cardBg,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  width: 133,
                  margin: const EdgeInsets.only(right: 12),
                  child: Shimmer.fromColors(
                    baseColor: AppTheme.cardBg,
                    highlightColor: AppTheme.borderColor,
                    child: Container(
                      decoration: BoxDecoration(
                        color: AppTheme.cardBg,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(HomeProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              provider.errorMessage ?? 'Please try again later',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => provider.loadHomeData(),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToPlayer(Map<String, dynamic> content) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlayerScreen(
          tmdbId: content['tmdb_id'],
          contentType: content['content_type'] ?? 
              (content.containsKey('title') ? 'movie' : 'tv_show'),
          title: content['title'] ?? content['name'],
        ),
      ),
    );
  }

  void _navigateToMovies(String filter) {
    // Navigate to movies screen with filter
  }

  void _navigateToTvShows(String filter) {
    // Navigate to TV shows screen with filter
  }

  void _navigateToWatchlist() {
    // Navigate to watchlist screen
  }

  void _showContentDetails(Map<String, dynamic> content) {
    // Show content details bottom sheet
  }

  void _showNotifications() {
    // Show notifications
  }

  void _showCastDialog() {
    // Show cast dialog
  }
}
