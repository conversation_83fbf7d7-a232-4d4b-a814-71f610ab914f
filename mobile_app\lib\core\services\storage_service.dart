import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static late Box _box;
  
  // Storage Keys
  static const String _userTokenKey = 'user_token';
  static const String _userDataKey = 'user_data';
  static const String _watchlistKey = 'watchlist';
  static const String _watchHistoryKey = 'watch_history';
  static const String _settingsKey = 'app_settings';
  static const String _downloadedContentKey = 'downloaded_content';
  static const String _recentSearchesKey = 'recent_searches';
  static const String _favoriteGenresKey = 'favorite_genres';
  
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _box = await Hive.openBox('streamflix_storage');
  }
  
  // Token Management
  static Future<void> saveToken(String token) async {
    await _prefs.setString(_userTokenKey, token);
  }
  
  static Future<String?> getToken() async {
    return _prefs.getString(_userTokenKey);
  }
  
  static Future<void> clearToken() async {
    await _prefs.remove(_userTokenKey);
  }
  
  static Future<bool> hasToken() async {
    return _prefs.containsKey(_userTokenKey);
  }
  
  // User Data Management
  static Future<void> saveUserData(Map<String, dynamic> userData) async {
    await _prefs.setString(_userDataKey, jsonEncode(userData));
  }
  
  static Future<Map<String, dynamic>?> getUserData() async {
    final userDataString = _prefs.getString(_userDataKey);
    if (userDataString != null) {
      return jsonDecode(userDataString);
    }
    return null;
  }
  
  static Future<void> clearUserData() async {
    await _prefs.remove(_userDataKey);
  }
  
  // Watchlist Management
  static Future<void> saveWatchlist(List<Map<String, dynamic>> watchlist) async {
    await _box.put(_watchlistKey, watchlist);
  }
  
  static List<Map<String, dynamic>> getWatchlist() {
    final watchlist = _box.get(_watchlistKey, defaultValue: <Map<String, dynamic>>[]);
    return List<Map<String, dynamic>>.from(watchlist);
  }
  
  static Future<void> addToWatchlist(Map<String, dynamic> content) async {
    final watchlist = getWatchlist();
    
    // Check if already exists
    final exists = watchlist.any((item) => 
      item['tmdb_id'] == content['tmdb_id'] && 
      item['content_type'] == content['content_type']
    );
    
    if (!exists) {
      watchlist.add({
        ...content,
        'added_at': DateTime.now().toIso8601String(),
      });
      await saveWatchlist(watchlist);
    }
  }
  
  static Future<void> removeFromWatchlist(int tmdbId, String contentType) async {
    final watchlist = getWatchlist();
    watchlist.removeWhere((item) => 
      item['tmdb_id'] == tmdbId && 
      item['content_type'] == contentType
    );
    await saveWatchlist(watchlist);
  }
  
  static bool isInWatchlist(int tmdbId, String contentType) {
    final watchlist = getWatchlist();
    return watchlist.any((item) => 
      item['tmdb_id'] == tmdbId && 
      item['content_type'] == contentType
    );
  }
  
  // Watch History Management
  static Future<void> saveWatchHistory(List<Map<String, dynamic>> history) async {
    await _box.put(_watchHistoryKey, history);
  }
  
  static List<Map<String, dynamic>> getWatchHistory() {
    final history = _box.get(_watchHistoryKey, defaultValue: <Map<String, dynamic>>[]);
    return List<Map<String, dynamic>>.from(history);
  }
  
  static Future<void> addToWatchHistory(Map<String, dynamic> content) async {
    final history = getWatchHistory();
    
    // Remove if already exists
    history.removeWhere((item) => 
      item['tmdb_id'] == content['tmdb_id'] && 
      item['content_type'] == content['content_type']
    );
    
    // Add to beginning
    history.insert(0, {
      ...content,
      'watched_at': DateTime.now().toIso8601String(),
    });
    
    // Keep only last 50 items
    if (history.length > 50) {
      history.removeRange(50, history.length);
    }
    
    await saveWatchHistory(history);
  }
  
  static Future<void> clearWatchHistory() async {
    await _box.delete(_watchHistoryKey);
  }
  
  // App Settings Management
  static Future<void> saveSettings(Map<String, dynamic> settings) async {
    await _prefs.setString(_settingsKey, jsonEncode(settings));
  }
  
  static Map<String, dynamic> getSettings() {
    final settingsString = _prefs.getString(_settingsKey);
    if (settingsString != null) {
      return jsonDecode(settingsString);
    }
    return _getDefaultSettings();
  }
  
  static Map<String, dynamic> _getDefaultSettings() {
    return {
      'video_quality': 'Auto',
      'subtitle_language': 'English',
      'auto_play_next': true,
      'download_quality': '720p',
      'cellular_download': false,
      'notifications_enabled': true,
      'dark_mode': true,
      'player_controls_timeout': 5,
      'seek_duration': 10,
      'volume': 1.0,
      'brightness': 1.0,
      'playback_speed': 1.0,
    };
  }
  
  static Future<void> updateSetting(String key, dynamic value) async {
    final settings = getSettings();
    settings[key] = value;
    await saveSettings(settings);
  }
  
  // Recent Searches Management
  static Future<void> saveRecentSearches(List<String> searches) async {
    await _box.put(_recentSearchesKey, searches);
  }
  
  static List<String> getRecentSearches() {
    final searches = _box.get(_recentSearchesKey, defaultValue: <String>[]);
    return List<String>.from(searches);
  }
  
  static Future<void> addRecentSearch(String query) async {
    final searches = getRecentSearches();
    
    // Remove if already exists
    searches.remove(query);
    
    // Add to beginning
    searches.insert(0, query);
    
    // Keep only last 10 searches
    if (searches.length > 10) {
      searches.removeRange(10, searches.length);
    }
    
    await saveRecentSearches(searches);
  }
  
  static Future<void> clearRecentSearches() async {
    await _box.delete(_recentSearchesKey);
  }
  
  // Favorite Genres Management
  static Future<void> saveFavoriteGenres(List<String> genres) async {
    await _box.put(_favoriteGenresKey, genres);
  }
  
  static List<String> getFavoriteGenres() {
    final genres = _box.get(_favoriteGenresKey, defaultValue: <String>[]);
    return List<String>.from(genres);
  }
  
  static Future<void> addFavoriteGenre(String genre) async {
    final genres = getFavoriteGenres();
    if (!genres.contains(genre)) {
      genres.add(genre);
      await saveFavoriteGenres(genres);
    }
  }
  
  static Future<void> removeFavoriteGenre(String genre) async {
    final genres = getFavoriteGenres();
    genres.remove(genre);
    await saveFavoriteGenres(genres);
  }
  
  // Downloaded Content Management
  static Future<void> saveDownloadedContent(List<Map<String, dynamic>> content) async {
    await _box.put(_downloadedContentKey, content);
  }
  
  static List<Map<String, dynamic>> getDownloadedContent() {
    final content = _box.get(_downloadedContentKey, defaultValue: <Map<String, dynamic>>[]);
    return List<Map<String, dynamic>>.from(content);
  }
  
  static Future<void> addDownloadedContent(Map<String, dynamic> content) async {
    final downloads = getDownloadedContent();
    
    // Check if already exists
    final exists = downloads.any((item) => 
      item['tmdb_id'] == content['tmdb_id'] && 
      item['content_type'] == content['content_type']
    );
    
    if (!exists) {
      downloads.add({
        ...content,
        'downloaded_at': DateTime.now().toIso8601String(),
      });
      await saveDownloadedContent(downloads);
    }
  }
  
  static Future<void> removeDownloadedContent(int tmdbId, String contentType) async {
    final downloads = getDownloadedContent();
    downloads.removeWhere((item) => 
      item['tmdb_id'] == tmdbId && 
      item['content_type'] == contentType
    );
    await saveDownloadedContent(downloads);
  }
  
  static bool isDownloaded(int tmdbId, String contentType) {
    final downloads = getDownloadedContent();
    return downloads.any((item) => 
      item['tmdb_id'] == tmdbId && 
      item['content_type'] == contentType
    );
  }
  
  // Cache Management
  static Future<void> clearCache() async {
    await _box.clear();
  }
  
  static Future<void> clearAllData() async {
    await _prefs.clear();
    await _box.clear();
  }
  
  // Storage Info
  static Future<Map<String, dynamic>> getStorageInfo() async {
    final keys = _box.keys.toList();
    final totalItems = keys.length;
    
    return {
      'total_items': totalItems,
      'watchlist_count': getWatchlist().length,
      'history_count': getWatchHistory().length,
      'downloads_count': getDownloadedContent().length,
      'recent_searches_count': getRecentSearches().length,
    };
  }
}
