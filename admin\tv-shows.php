<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Handle actions
if ($_POST['action'] ?? '' === 'toggle_featured') {
    $show_id = (int)($_POST['show_id'] ?? 0);
    if ($show_id > 0) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("UPDATE tv_shows SET is_featured = NOT is_featured WHERE id = :id");
            $stmt->execute([':id' => $show_id]);
            $message = 'TV show featured status updated!';
        } catch (Exception $e) {
            $error = 'Failed to update TV show: ' . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'toggle_trending') {
    $show_id = (int)($_POST['show_id'] ?? 0);
    if ($show_id > 0) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("UPDATE tv_shows SET is_trending = NOT is_trending WHERE id = :id");
            $stmt->execute([':id' => $show_id]);
            $message = 'TV show trending status updated!';
        } catch (Exception $e) {
            $error = 'Failed to update TV show: ' . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'delete_show') {
    $show_id = (int)($_POST['show_id'] ?? 0);
    if ($show_id > 0) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("DELETE FROM tv_shows WHERE id = :id");
            $stmt->execute([':id' => $show_id]);
            $message = 'TV show deleted successfully!';
        } catch (Exception $e) {
            $error = 'Failed to delete TV show: ' . $e->getMessage();
        }
    }
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get TV shows with pagination
    $stmt = $conn->prepare("
        SELECT t.*, COUNT(*) OVER() as total_count
        FROM tv_shows t 
        ORDER BY t.created_at DESC 
        LIMIT :limit OFFSET :offset
    ");
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $tv_shows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_count = $tv_shows[0]['total_count'] ?? 0;
    $total_pages = ceil($total_count / $limit);
    
} catch (Exception $e) {
    $tv_shows = [];
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage TV Shows - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .shows-table {
            background: var(--secondary-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .table th {
            background: var(--dark-bg);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .table td {
            color: var(--text-secondary);
        }
        
        .show-poster {
            width: 60px;
            height: 90px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .show-title {
            color: var(--text-primary);
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-featured {
            background: var(--primary-color);
            color: white;
        }
        
        .badge-trending {
            background: #ffd700;
            color: #000;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .btn-toggle {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .btn-toggle:hover {
            background: var(--primary-color);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: var(--primary-color);
        }
        
        .pagination .current {
            background: var(--primary-color);
        }
        
        .bulk-actions {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .bulk-actions h3 {
            margin-bottom: 15px;
            color: var(--text-primary);
        }
        
        .bulk-btn {
            margin: 5px;
        }
        
        .show-info {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Manage TV Shows</h1>
            <p>View and manage all TV shows in your database</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php" class="active">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php">Maintenance</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <div class="bulk-actions">
            <h3>Bulk Import</h3>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('trending_tv')">Import Trending TV Shows</button>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('popular_tv')">Import Popular TV Shows</button>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('airing_today')">Import Airing Today</button>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('on_the_air')">Import On The Air</button>
        </div>

        <!-- TV Shows Table -->
        <div class="shows-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Poster</th>
                        <th>Name</th>
                        <th>Year</th>
                        <th>Rating</th>
                        <th>Seasons/Episodes</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($tv_shows as $show): ?>
                    <tr>
                        <td>
                            <img src="<?php echo getImageUrl($show['poster_path'], 'w200'); ?>" 
                                 alt="<?php echo htmlspecialchars($show['name']); ?>" 
                                 class="show-poster">
                        </td>
                        <td>
                            <div class="show-title"><?php echo htmlspecialchars($show['name']); ?></div>
                            <small>TMDB ID: <?php echo $show['tmdb_id']; ?></small>
                        </td>
                        <td><?php echo date('Y', strtotime($show['first_air_date'])); ?></td>
                        <td>★ <?php echo $show['vote_average']; ?></td>
                        <td>
                            <div class="show-info">
                                <?php echo $show['number_of_seasons']; ?> Season<?php echo $show['number_of_seasons'] != 1 ? 's' : ''; ?>
                            </div>
                            <div class="show-info">
                                <?php echo $show['number_of_episodes']; ?> Episodes
                            </div>
                        </td>
                        <td>
                            <?php if ($show['is_featured']): ?>
                                <span class="status-badge badge-featured">Featured</span>
                            <?php endif; ?>
                            <?php if ($show['is_trending']): ?>
                                <span class="status-badge badge-trending">Trending</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_featured">
                                <input type="hidden" name="show_id" value="<?php echo $show['id']; ?>">
                                <button type="submit" class="action-btn btn-toggle">
                                    <?php echo $show['is_featured'] ? 'Unfeature' : 'Feature'; ?>
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_trending">
                                <input type="hidden" name="show_id" value="<?php echo $show['id']; ?>">
                                <button type="submit" class="action-btn btn-toggle">
                                    <?php echo $show['is_trending'] ? 'Untrend' : 'Trend'; ?>
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this TV show?')">
                                <input type="hidden" name="action" value="delete_show">
                                <input type="hidden" name="show_id" value="<?php echo $show['id']; ?>">
                                <button type="submit" class="action-btn btn-delete">Delete</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>">← Previous</a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">Next →</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        async function bulkImport(type) {
            if (!confirm(`Import ${type.replace('_', ' ')}? This may take a while.`)) {
                return;
            }
            
            try {
                const response = await fetch('../api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_' + type,
                        type: 'tv'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Successfully imported ${data.count} TV shows`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
