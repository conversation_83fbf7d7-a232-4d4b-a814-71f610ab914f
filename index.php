<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/config/database.php')) {
    header('Location: install.php');
    exit('Please run the installer first.');
}

require_once 'includes/functions.php';

$streamflix = new StreamFlix();

// Get featured and trending content
$featured_movies = [];
$trending_movies = [];
$featured_tv = [];
$trending_tv = [];

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get featured movies
    $stmt = $conn->prepare("SELECT * FROM movies WHERE is_featured = 1 ORDER BY popularity DESC LIMIT 10");
    $stmt->execute();
    $featured_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get trending movies
    $stmt = $conn->prepare("SELECT * FROM movies WHERE is_trending = 1 ORDER BY popularity DESC LIMIT 20");
    $stmt->execute();
    $trending_movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get featured TV shows
    $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE is_featured = 1 ORDER BY popularity DESC LIMIT 10");
    $stmt->execute();
    $featured_tv = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get trending TV shows
    $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE is_trending = 1 ORDER BY popularity DESC LIMIT 20");
    $stmt->execute();
    $trending_tv = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get hero content (random featured movie or TV show)
    $all_featured = array_merge($featured_movies, $featured_tv);
    $hero_content = !empty($all_featured) ? $all_featured[array_rand($all_featured)] : null;
    
} catch (Exception $e) {
    error_log("Database error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - Premium Movie & TV Streaming</title>
    <meta name="description" content="Watch unlimited movies and TV shows online. Stream the latest releases and classic favorites in HD quality.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="assets/images/icon-192x192.png">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#e50914">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="StreamFlix">

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Meta Tags -->
    <meta property="og:title" content="<?php echo SITE_NAME; ?> - Premium Streaming">
    <meta property="og:description" content="Watch unlimited movies and TV shows online">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>

            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">☰</button>

            <ul class="nav-links" id="navLinks">
                <li><a href="index.php" class="active">Home</a></li>
                <li><a href="movies.php">Movies</a></li>
                <li><a href="tv-shows.php">TV Shows</a></li>
                <li><a href="genres.php">Genres</a></li>
                <?php if (isLoggedIn()): ?>
                    <li><a href="watchlist.php">My List</a></li>
                <?php endif; ?>
            </ul>

            <div class="user-menu">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="Search movies, TV shows...">
                    <button class="search-btn">🔍</button>
                    <div class="search-results"></div>
                </div>

                <?php if (isLoggedIn()): ?>
                    <div class="user-dropdown">
                        <button class="user-btn"><?php echo $_SESSION['username']; ?></button>
                        <div class="dropdown-menu">
                            <a href="profile.php">Profile</a>
                            <a href="watchlist.php">My List</a>
                            <?php if (isAdmin()): ?>
                                <a href="admin/">Admin Panel</a>
                            <?php endif; ?>
                            <a href="logout.php">Logout</a>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="btn btn-primary">Sign In</a>
                <?php endif; ?>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <?php if ($hero_content): ?>
    <section class="hero" style="background-image: url('<?php echo getImageUrl($hero_content['backdrop_path'], 'original'); ?>');">
        <div class="hero-content">
            <div class="hero-meta">
                <span class="hero-year"><?php echo date('Y', strtotime($hero_content['release_date'] ?? $hero_content['first_air_date'])); ?></span>
                <div class="hero-rating">
                    <span class="star">★</span>
                    <span><?php echo $hero_content['vote_average']; ?></span>
                </div>
                <?php if (isset($hero_content['runtime']) && $hero_content['runtime']): ?>
                    <span class="hero-runtime"><?php echo formatRuntime($hero_content['runtime']); ?></span>
                <?php endif; ?>
            </div>

            <h1 class="hero-title"><?php echo htmlspecialchars($hero_content['title'] ?? $hero_content['name']); ?></h1>
            <p class="hero-description"><?php echo htmlspecialchars(substr($hero_content['overview'], 0, 250)) . '...'; ?></p>

            <div class="hero-buttons">
                <a href="player.php?id=<?php echo $hero_content['tmdb_id']; ?>&type=<?php echo isset($hero_content['title']) ? 'movie' : 'tv_show'; ?>" class="btn btn-primary">
                    ▶ Play
                </a>
                <button class="btn btn-secondary watchlist-btn" data-id="<?php echo $hero_content['id']; ?>" data-type="<?php echo isset($hero_content['title']) ? 'movie' : 'tv_show'; ?>">
                    ⊕ My List
                </button>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Featured Movies -->
        <?php if (!empty($featured_movies)): ?>
        <section class="content-section">
            <h2 class="section-title">Featured Movies</h2>
            <div class="content-row">
                <button class="slider-nav prev" onclick="slideContent(this, 'prev')">‹</button>
                <button class="slider-nav next" onclick="slideContent(this, 'next')">›</button>
                <div class="content-slider" id="featuredMoviesSlider">
                    <?php foreach ($featured_movies as $movie): ?>
                    <div class="content-card slider-card" data-id="<?php echo $movie['id']; ?>" data-type="movie">
                        <img src="<?php echo getImageUrl($movie['poster_path'], 'w500'); ?>"
                             alt="<?php echo htmlspecialchars($movie['title']); ?>"
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <div class="card-info">
                                <span><?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                                <div class="rating">
                                    <span class="star">★</span>
                                    <span><?php echo $movie['vote_average']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Trending Movies -->
        <?php if (!empty($trending_movies)): ?>
        <section class="content-section">
            <h2 class="section-title">Trending Movies</h2>
            <div class="content-row">
                <div class="content-slider">
                    <?php foreach ($trending_movies as $movie): ?>
                    <div class="content-card slider-card" data-id="<?php echo $movie['id']; ?>" data-type="movie">
                        <img src="<?php echo getImageUrl($movie['poster_path'], 'w500'); ?>" 
                             alt="<?php echo htmlspecialchars($movie['title']); ?>" 
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <div class="card-info">
                                <span><?php echo date('Y', strtotime($movie['release_date'])); ?></span>
                                <div class="rating">
                                    <span class="star">★</span>
                                    <span><?php echo $movie['vote_average']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Featured TV Shows -->
        <?php if (!empty($featured_tv)): ?>
        <section class="content-section">
            <h2 class="section-title">Featured TV Shows</h2>
            <div class="content-row">
                <div class="content-slider">
                    <?php foreach ($featured_tv as $show): ?>
                    <div class="content-card slider-card" data-id="<?php echo $show['id']; ?>" data-type="tv_show">
                        <img src="<?php echo getImageUrl($show['poster_path'], 'w500'); ?>" 
                             alt="<?php echo htmlspecialchars($show['name']); ?>" 
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($show['name']); ?></div>
                            <div class="card-info">
                                <span><?php echo date('Y', strtotime($show['first_air_date'])); ?></span>
                                <div class="rating">
                                    <span class="star">★</span>
                                    <span><?php echo $show['vote_average']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>

        <!-- Trending TV Shows -->
        <?php if (!empty($trending_tv)): ?>
        <section class="content-section">
            <h2 class="section-title">Trending TV Shows</h2>
            <div class="content-row">
                <div class="content-slider">
                    <?php foreach ($trending_tv as $show): ?>
                    <div class="content-card slider-card" data-id="<?php echo $show['id']; ?>" data-type="tv_show">
                        <img src="<?php echo getImageUrl($show['poster_path'], 'w500'); ?>" 
                             alt="<?php echo htmlspecialchars($show['name']); ?>" 
                             loading="lazy">
                        <div class="card-overlay">
                            <div class="card-title"><?php echo htmlspecialchars($show['name']); ?></div>
                            <div class="card-info">
                                <span><?php echo date('Y', strtotime($show['first_air_date'])); ?></span>
                                <div class="rating">
                                    <span class="star">★</span>
                                    <span><?php echo $show['vote_average']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>
    </main>

    <!-- Content Modal -->
    <div id="contentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-body">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3><?php echo SITE_NAME; ?></h3>
                <p>Premium movie and TV show streaming platform</p>
            </div>
            <div class="footer-section">
                <h4>Quick Links</h4>
                <ul>
                    <li><a href="movies.php">Movies</a></li>
                    <li><a href="tv-shows.php">TV Shows</a></li>
                    <li><a href="genres.php">Genres</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h4>Support</h4>
                <ul>
                    <li><a href="contact.php">Contact Us</a></li>
                    <li><a href="privacy.php">Privacy Policy</a></li>
                    <li><a href="terms.php">Terms of Service</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('active');
        }

        // Slider navigation
        function slideContent(button, direction) {
            const slider = button.parentElement.querySelector('.content-slider');
            const cardWidth = slider.querySelector('.slider-card').offsetWidth + 8; // including gap
            const scrollAmount = cardWidth * 3; // scroll 3 cards at a time

            if (direction === 'prev') {
                slider.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
            } else {
                slider.scrollBy({ left: scrollAmount, behavior: 'smooth' });
            }
        }

        // Auto-hide header on scroll (mobile)
        let lastScrollTop = 0;
        const header = document.querySelector('.header');

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (window.innerWidth <= 768) {
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down
                    header.style.transform = 'translateY(-100%)';
                } else {
                    // Scrolling up
                    header.style.transform = 'translateY(0)';
                }
            }

            lastScrollTop = scrollTop;
        });

        // Touch gestures for mobile
        if ('ontouchstart' in window) {
            document.querySelectorAll('.content-slider').forEach(slider => {
                let startX = 0;
                let scrollLeft = 0;

                slider.addEventListener('touchstart', (e) => {
                    startX = e.touches[0].pageX - slider.offsetLeft;
                    scrollLeft = slider.scrollLeft;
                });

                slider.addEventListener('touchmove', (e) => {
                    e.preventDefault();
                    const x = e.touches[0].pageX - slider.offsetLeft;
                    const walk = (x - startX) * 2;
                    slider.scrollLeft = scrollLeft - walk;
                });
            });
        }

        // Intersection Observer for lazy loading
        const observerOptions = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        }, observerOptions);

        // Observe all images
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });

        // PWA-like features
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }

        // Add to home screen prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button if needed
            const installBtn = document.getElementById('installBtn');
            if (installBtn) {
                installBtn.style.display = 'block';
                installBtn.addEventListener('click', () => {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        deferredPrompt = null;
                        installBtn.style.display = 'none';
                    });
                });
            }
        });
    </script>
</body>
</html>
