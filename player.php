<?php
require_once 'includes/functions.php';

// Get parameters
$tmdb_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'movie';
$season = isset($_GET['season']) ? (int)$_GET['season'] : 1;
$episode = isset($_GET['episode']) ? (int)$_GET['episode'] : 1;

if (!$tmdb_id) {
    header('Location: index.php');
    exit();
}

$streamflix = new StreamFlix();

// Get content details
$content = null;
try {
    $db = new Database();
    $conn = $db->connect();

    if ($type === 'movie') {
        $stmt = $conn->prepare("SELECT * FROM movies WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get episode details for TV shows
        if ($content) {
            $stmt = $conn->prepare("
                SELECT e.*, s.season_number
                FROM episodes e
                JOIN seasons s ON e.season_id = s.id
                WHERE s.tv_show_id = :tv_show_id AND s.season_number = :season AND e.episode_number = :episode
            ");
            $stmt->execute([
                ':tv_show_id' => $content['id'],
                ':season' => $season,
                ':episode' => $episode
            ]);
            $episode_data = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
} catch (Exception $e) {
    error_log("Player error: " . $e->getMessage());
    header('Location: index.php');
    exit();
}

if (!$content) {
    header('Location: index.php');
    exit();
}

// Generate embed URLs
$embed_urls = $streamflix->getEmbedUrls($type, $tmdb_id, $season, $episode);

$title = $content['title'] ?? $content['name'];
if ($type === 'tv_show') {
    $title .= " - S{$season}E{$episode}";
    if (isset($episode_data) && $episode_data['name']) {
        $title .= ": " . $episode_data['name'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($title); ?> - <?php echo SITE_NAME; ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Ad Blocking CSS -->
    <style>
        /* Block common ad elements */
        .ads, .ad, .advertisement, .google-ads, .adsystem, .adsbygoogle,
        [class*="ad-"], [id*="ad-"], [class*="ads-"], [id*="ads-"],
        iframe[src*="googlesyndication"], iframe[src*="doubleclick"],
        iframe[src*="googletagmanager"], iframe[src*="facebook.com/tr"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
        }
        
        /* Player specific styles */
        .player-container {
            background: #000;
            position: relative;
            margin: 80px auto 20px;
            max-width: 1200px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
        }
        
        .player-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
        }
        
        .player-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            background: #000;
        }
        
        .server-selector {
            background: var(--secondary-color);
            padding: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .server-label {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .server-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .server-btn {
            padding: 10px 20px;
            background: var(--border-color);
            color: var(--text-primary);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .server-btn:hover {
            background: var(--primary-color);
        }
        
        .server-btn.active {
            background: var(--primary-color);
            box-shadow: 0 2px 8px rgba(229, 9, 20, 0.3);
        }
        
        .content-info {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--secondary-color);
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .content-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        
        .content-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            color: var(--text-secondary);
        }
        
        .content-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--text-primary);
            text-decoration: none;
            padding: 10px 20px;
            background: var(--border-color);
            border-radius: 4px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background: var(--primary-color);
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }
        
        .error-message h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .player-container {
                margin: 70px 10px 20px;
            }
            
            .content-info {
                margin: 0 10px 20px;
                padding: 15px;
            }
            
            .content-title {
                font-size: 1.5rem;
            }
            
            .server-buttons {
                justify-content: center;
            }
            
            .server-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header scrolled">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="javascript:history.back()" class="back-btn">← Back</a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Content Info -->
        <div class="content-info">
            <h1 class="content-title"><?php echo htmlspecialchars($title); ?></h1>
            <div class="content-meta">
                <span><?php echo date('Y', strtotime($content['release_date'] ?? $content['first_air_date'])); ?></span>
                <span>★ <?php echo $content['vote_average']; ?>/10</span>
                <?php if ($content['runtime']): ?>
                    <span><?php echo formatRuntime($content['runtime']); ?></span>
                <?php endif; ?>
            </div>
            <?php if ($content['overview']): ?>
                <p class="content-description"><?php echo htmlspecialchars($content['overview']); ?></p>
            <?php endif; ?>
        </div>

        <!-- Player -->
        <div class="player-container">
            <div class="player-wrapper">
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-spinner"></div>
                </div>
                
                <iframe 
                    id="playerIframe" 
                    class="player-iframe" 
                    src="<?php echo $embed_urls[0]['url']; ?>"
                    allowfullscreen
                    webkitallowfullscreen
                    mozallowfullscreen
                    allow="autoplay; fullscreen; picture-in-picture"
                    sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-presentation"
                    referrerpolicy="no-referrer">
                </iframe>
            </div>
            
            <!-- Server Selector -->
            <div class="server-selector">
                <div class="server-label">Select Server:</div>
                <div class="server-buttons">
                    <?php foreach ($embed_urls as $index => $server): ?>
                        <button 
                            class="server-btn <?php echo $index === 0 ? 'active' : ''; ?>" 
                            data-url="<?php echo htmlspecialchars($server['url']); ?>"
                            data-server="<?php echo htmlspecialchars($server['name']); ?>">
                            <?php echo htmlspecialchars($server['name']); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Episode Selector for TV Shows -->
        <?php if ($type === 'tv_show'): ?>
        <div class="episode-selector">
            <h3>Episodes</h3>
            <div class="episode-grid" id="episodeGrid">
                <!-- Episodes will be loaded here -->
            </div>
        </div>
        <?php endif; ?>
    </main>

    <!-- Ad Blocking JavaScript -->
    <script>
        // Advanced Ad Blocking
        (function() {
            'use strict';
            
            // Block ad domains
            const adDomains = [
                'googlesyndication.com',
                'doubleclick.net',
                'googletagmanager.com',
                'google-analytics.com',
                'facebook.com/tr',
                'analytics.google.com',
                'ads.yahoo.com',
                'adsystem.amazon.com'
            ];
            
            // Override fetch to block ad requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string') {
                    for (const domain of adDomains) {
                        if (url.includes(domain)) {
                            return Promise.reject(new Error('Blocked'));
                        }
                    }
                }
                return originalFetch.apply(this, args);
            };
            
            // Block ad scripts
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            // Block script tags with ad domains
                            if (node.tagName === 'SCRIPT' && node.src) {
                                for (const domain of adDomains) {
                                    if (node.src.includes(domain)) {
                                        node.remove();
                                        return;
                                    }
                                }
                            }
                            
                            // Block iframe ads
                            if (node.tagName === 'IFRAME' && node.src) {
                                for (const domain of adDomains) {
                                    if (node.src.includes(domain)) {
                                        node.remove();
                                        return;
                                    }
                                }
                            }
                            
                            // Remove elements with ad-related classes/ids
                            const adSelectors = [
                                '[class*="ad"]', '[id*="ad"]', '[class*="ads"]', '[id*="ads"]',
                                '.advertisement', '.google-ads', '.adsense'
                            ];
                            
                            adSelectors.forEach(selector => {
                                try {
                                    const adElements = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                                    adElements.forEach(el => el.remove());
                                } catch (e) {}
                            });
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        })();
        
        // Player functionality
        document.addEventListener('DOMContentLoaded', function() {
            const playerIframe = document.getElementById('playerIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const serverButtons = document.querySelectorAll('.server-btn');
            
            // Hide loading overlay when iframe loads
            playerIframe.addEventListener('load', function() {
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 1000);
            });
            
            // Server switching
            serverButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const url = this.dataset.url;
                    const serverName = this.dataset.server;
                    
                    // Show loading
                    loadingOverlay.style.display = 'flex';
                    
                    // Update active button
                    serverButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Change iframe source
                    playerIframe.src = url;
                    
                    console.log('Switched to server:', serverName);
                });
            });
            
            // Error handling
            playerIframe.addEventListener('error', function() {
                loadingOverlay.innerHTML = `
                    <div class="error-message">
                        <h3>Player Error</h3>
                        <p>Unable to load this server. Please try another server.</p>
                        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
                    </div>
                `;
            });
            
            // Prevent right-click on player
            playerIframe.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
        });
        
        <?php if ($type === 'tv_show'): ?>
        // Load episodes for TV shows
        async function loadEpisodes() {
            try {
                const response = await fetch(`api/episodes.php?id=<?php echo $content['id']; ?>&season=<?php echo $season; ?>`);
                const data = await response.json();
                
                if (data.success) {
                    displayEpisodes(data.episodes);
                }
            } catch (error) {
                console.error('Error loading episodes:', error);
            }
        }
        
        function displayEpisodes(episodes) {
            const episodeGrid = document.getElementById('episodeGrid');
            episodeGrid.innerHTML = episodes.map(ep => `
                <div class="episode-card ${ep.episode_number == <?php echo $episode; ?> ? 'active' : ''}" 
                     onclick="playEpisode(<?php echo $season; ?>, ${ep.episode_number})">
                    <div class="episode-number">E${ep.episode_number}</div>
                    <div class="episode-title">${ep.name || 'Episode ' + ep.episode_number}</div>
                    <div class="episode-runtime">${ep.runtime ? ep.runtime + 'm' : ''}</div>
                </div>
            `).join('');
        }
        
        function playEpisode(season, episode) {
            const url = `player.php?id=<?php echo $tmdb_id; ?>&type=tv_show&season=${season}&episode=${episode}`;
            window.location.href = url;
        }
        
        // Load episodes on page load
        loadEpisodes();
        <?php endif; ?>
    </script>
</body>
</html>
