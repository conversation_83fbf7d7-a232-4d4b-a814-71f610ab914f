plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.streamflix.app"
    compileSdk 34
    ndkVersion "25.1.8937393"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.streamflix.app"
        minSdk 21
        targetSdk 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        
        // Multidex support
        multiDexEnabled true
        
        // Vector drawables support
        vectorDrawables.useSupportLibrary true
        
        // TV support
        resConfigs "en", "es", "fr", "de"
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            // Configure release signing
            if (project.hasProperty('STREAMFLIX_UPLOAD_STORE_FILE')) {
                storeFile file(STREAMFLIX_UPLOAD_STORE_FILE)
                storePassword STREAMFLIX_UPLOAD_STORE_PASSWORD
                keyAlias STREAMFLIX_UPLOAD_KEY_ALIAS
                keyPassword STREAMFLIX_UPLOAD_KEY_PASSWORD
            }
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
            debuggable true
            minifyEnabled false
            shrinkResources false
            
            // Debug build config
            buildConfigField "String", "API_BASE_URL", '"http://10.0.2.2:8000"'
            buildConfigField "boolean", "DEBUG_MODE", "true"
        }
        
        release {
            signingConfig signingConfigs.release
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // Release build config
            buildConfigField "String", "API_BASE_URL", '"https://your-domain.com"'
            buildConfigField "boolean", "DEBUG_MODE", "false"
        }
        
        profile {
            signingConfig signingConfigs.debug
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // Profile build config
            buildConfigField "String", "API_BASE_URL", '"https://staging.your-domain.com"'
            buildConfigField "boolean", "DEBUG_MODE", "false"
        }
    }

    // Product flavors for different variants
    flavorDimensions "version"
    productFlavors {
        mobile {
            dimension "version"
            applicationIdSuffix ""
            versionNameSuffix ""
        }
        
        tv {
            dimension "version"
            applicationIdSuffix ".tv"
            versionNameSuffix "-tv"
            
            // TV specific configurations
            resValue "string", "app_name", "StreamFlix TV"
        }
    }

    // Packaging options
    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }

    // Lint options
    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    // Bundle configuration
    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    
    // AndroidX libraries
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.activity:activity-ktx:1.8.2'
    implementation 'androidx.fragment:fragment-ktx:1.6.2'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    
    // Material Design
    implementation 'com.google.android.material:material:1.11.0'
    
    // TV support
    implementation 'androidx.leanback:leanback:1.0.0'
    implementation 'androidx.leanback:leanback-preference:1.0.0'
    implementation 'androidx.tvprovider:tvprovider:1.0.0'
    
    // Media libraries
    implementation 'androidx.media:media:1.7.0'
    implementation 'androidx.media2:media2-session:1.3.0'
    implementation 'androidx.media2:media2-widget:1.3.0'
    implementation 'androidx.media2:media2-player:1.3.0'
    
    // ExoPlayer for advanced video playback
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.19.1'
    implementation 'com.google.android.exoplayer:exoplayer-cast:2.19.1'
    
    // Google Cast
    implementation 'com.google.android.gms:play-services-cast-framework:21.4.0'
    
    // Network libraries
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    
    // Image loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    
    // Multidex
    implementation 'androidx.multidex:multidex:2.0.1'
    
    // Work Manager for background tasks
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
    
    // Room database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    
    // Biometric authentication
    implementation 'androidx.biometric:biometric:1.1.0'
    
    // In-app updates
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:app-update-ktx:2.1.0'
    
    // Firebase (optional)
    // implementation 'com.google.firebase:firebase-analytics:21.5.0'
    // implementation 'com.google.firebase:firebase-crashlytics:18.6.1'
    // implementation 'com.google.firebase:firebase-messaging:23.4.0'
}

// Apply Google Services plugin if using Firebase
// apply plugin: 'com.google.gms.google-services'
