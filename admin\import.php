<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

// Handle import actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'import_letsembed') {
        $type = sanitizeInput($_POST['type'] ?? '');
        $limit = (int)($_POST['limit'] ?? 50);
        
        if (in_array($type, ['movie', 'tv', 'anime', 'hentai'])) {
            try {
                $imported = importFromLetsEmbed($type, $limit);
                $message = "Successfully imported {$imported} {$type}s from TMDB!";

                // Add debug information
                if ($imported == 0) {
                    $error = "No content was imported. This might be due to: 1) All content already exists, 2) TMDB API issues, 3) Database connection problems. Check your TMDB API key in settings.";
                }
            } catch (Exception $e) {
                $error = 'Import failed: ' . $e->getMessage();
            }
        } else {
            $error = 'Invalid content type selected.';
        }
    }
    
    if ($action === 'import_custom_json') {
        $json_url = sanitizeInput($_POST['json_url'] ?? '');
        $content_type = sanitizeInput($_POST['content_type'] ?? '');
        
        if (!empty($json_url) && in_array($content_type, ['movie', 'tv_show'])) {
            try {
                $imported = importFromCustomJSON($json_url, $content_type);
                $message = "Successfully imported {$imported} items from custom JSON!";
            } catch (Exception $e) {
                $error = 'Import failed: ' . $e->getMessage();
            }
        } else {
            $error = 'Please provide valid JSON URL and content type.';
        }
    }
}

function importFromLetsEmbed($type, $limit = 50) {
    // For now, we'll use TMDB popular/trending lists since LetsEmbed API might not be accessible
    // You can replace this with actual LetsEmbed API when available

    $streamflix = new StreamFlix();
    $imported = 0;
    $errors = [];

    try {
        if (in_array($type, ['movie', 'anime'])) {
            // Import popular movies
            $endpoint = '/movie/popular';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                $items = array_slice($data['results'], 0, $limit);

                foreach ($items as $item) {
                    try {
                        // Check if movie already exists
                        $db = new Database();
                        $conn = $db->connect();
                        $stmt = $conn->prepare("SELECT id FROM movies WHERE tmdb_id = ?");
                        $stmt->execute([$item['id']]);

                        if ($stmt->rowCount() > 0) {
                            // Movie already exists, skip
                            continue;
                        }

                        $result = $streamflix->importMovie($item['id']);
                        if ($result) {
                            $imported++;

                            // Mark anime content
                            if ($type === 'anime') {
                                markAsAnime($result, 'movie');
                            }
                        }

                        // Small delay to avoid overwhelming TMDB API
                        usleep(500000); // 0.5 seconds

                    } catch (Exception $e) {
                        $errors[] = "Error importing movie {$item['id']}: " . $e->getMessage();
                        continue;
                    }
                }
            } else {
                throw new Exception('No data received from TMDB movies API');
            }
        } else {
            // Import popular TV shows
            $endpoint = '/tv/popular';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                $items = array_slice($data['results'], 0, $limit);

                foreach ($items as $item) {
                    try {
                        // Check if TV show already exists
                        $db = new Database();
                        $conn = $db->connect();
                        $stmt = $conn->prepare("SELECT id FROM tv_shows WHERE tmdb_id = ?");
                        $stmt->execute([$item['id']]);

                        if ($stmt->rowCount() > 0) {
                            // TV show already exists, skip
                            continue;
                        }

                        $result = $streamflix->importTVShow($item['id']);
                        if ($result) {
                            $imported++;

                            // Mark anime content
                            if ($type === 'anime') {
                                markAsAnime($result, 'tv_show');
                            }
                        }

                        // Small delay to avoid overwhelming TMDB API
                        usleep(500000); // 0.5 seconds

                    } catch (Exception $e) {
                        $errors[] = "Error importing TV show {$item['id']}: " . $e->getMessage();
                        continue;
                    }
                }
            } else {
                throw new Exception('No data received from TMDB TV API');
            }
        }

        // Log errors if any
        if (!empty($errors)) {
            error_log("Import errors: " . implode(", ", $errors));
        }

    } catch (Exception $e) {
        throw new Exception('Failed to import from TMDB: ' . $e->getMessage());
    }

    return $imported;
}

function importFromCustomJSON($json_url, $content_type) {
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (compatible; StreamFlix/1.0)'
        ]
    ]);
    
    $json_data = @file_get_contents($json_url, false, $context);
    
    if ($json_data === false) {
        throw new Exception('Failed to fetch JSON from provided URL');
    }
    
    $data = json_decode($json_data, true);
    
    if (!$data || !is_array($data)) {
        throw new Exception('Invalid JSON format');
    }
    
    $streamflix = new StreamFlix();
    $imported = 0;
    
    foreach ($data as $item) {
        if (!isset($item['tmdb_id']) || !$item['tmdb_id']) {
            continue;
        }
        
        try {
            if ($content_type === 'movie') {
                $result = $streamflix->importMovie($item['tmdb_id']);
            } else {
                $result = $streamflix->importTVShow($item['tmdb_id']);
            }
            
            if ($result) {
                $imported++;
            }
            
            usleep(200000); // 0.2 seconds delay
            
        } catch (Exception $e) {
            continue;
        }
    }
    
    return $imported;
}

function markAsAnime($content_id, $content_type) {
    try {
        $db = new Database();
        $conn = $db->connect();

        // Create anime genre if it doesn't exist
        $stmt = $conn->prepare("INSERT IGNORE INTO genres (name, tmdb_id) VALUES (?, ?)");
        $stmt->execute(['Anime', 16]); // 16 is TMDB's Animation genre ID

        $stmt = $conn->prepare("SELECT id FROM genres WHERE name = 'Anime'");
        $stmt->execute();
        $genre = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($genre) {
            if ($content_type === 'movie') {
                // Add to movie_genres
                $stmt = $conn->prepare("INSERT IGNORE INTO movie_genres (movie_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$content_id, $genre['id']]);
            } else {
                // Add to tv_show_genres
                $stmt = $conn->prepare("INSERT IGNORE INTO tv_show_genres (tv_show_id, genre_id) VALUES (?, ?)");
                $stmt->execute([$content_id, $genre['id']]);
            }
        }
    } catch (Exception $e) {
        // Ignore errors in genre assignment
        error_log("Error marking as anime: " . $e->getMessage());
    }
}

// Get import statistics
try {
    $db = new Database();
    $conn = $db->connect();
    
    $stats = [];
    $stmt = $conn->query("SELECT COUNT(*) as count FROM movies WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['movies_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM tv_shows WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
    $stats['tv_shows_today'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
} catch (Exception $e) {
    $stats = ['movies_today' => 0, 'tv_shows_today' => 0];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Import - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .import-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .import-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }
        
        .section-description {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 1rem;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .import-stats {
            background: var(--dark-bg);
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .letsembed-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .option-card {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .option-card:hover,
        .option-card.selected {
            border-color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }
        
        .option-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }
        
        .option-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--border-color);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            color: #ffc107;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .warning-box h4 {
            margin-bottom: 10px;
        }
        
        @media (max-width: 768px) {
            .import-grid {
                grid-template-columns: 1fr;
            }
            
            .letsembed-options {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Content Import</h1>
            <p>Import movies, TV shows, anime, and other content from various sources</p>
            <div style="margin-top: 10px;">
                <a href="test-import.php" class="btn btn-secondary" style="background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">🧪 Test Import System</a>
            </div>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php" class="active">Import</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Import Statistics -->
        <div class="import-stats">
            <h3 style="color: var(--text-primary); margin-bottom: 15px;">Today's Imports</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $stats['movies_today']; ?></div>
                    <div class="stat-label">Movies Added</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $stats['tv_shows_today']; ?></div>
                    <div class="stat-label">TV Shows Added</div>
                </div>
            </div>
        </div>

        <div class="import-grid">
            <!-- LetsEmbed Import -->
            <div class="import-section">
                <h2 class="section-title">LetsEmbed Import</h2>
                <p class="section-description">
                    Import content from TMDB's popular and trending lists. Content will be automatically categorized and marked as featured/trending.
                </p>

                <div style="background: rgba(23, 162, 184, 0.1); border: 1px solid #17a2b8; color: #17a2b8; padding: 15px; border-radius: 6px; margin-bottom: 20px;">
                    <strong>ℹ️ Note:</strong> Currently importing from TMDB popular/trending lists. LetsEmbed API integration can be added when their API is accessible.
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ Important Notes:</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li>Large imports may take several minutes</li>
                        <li>Duplicate content will be skipped automatically</li>
                        <li>TMDB API rate limits apply</li>
                    </ul>
                </div>
                
                <form method="POST" id="letsembedForm">
                    <input type="hidden" name="action" value="import_letsembed">
                    
                    <div class="form-group">
                        <label>Select Content Type:</label>
                        <div class="letsembed-options">
                            <div class="option-card" onclick="selectOption('movie')">
                                <div class="option-title">Movies</div>
                                <div class="option-description">Latest movies</div>
                            </div>
                            <div class="option-card" onclick="selectOption('tv')">
                                <div class="option-title">TV Shows</div>
                                <div class="option-description">Popular series</div>
                            </div>
                            <div class="option-card" onclick="selectOption('anime')">
                                <div class="option-title">Anime</div>
                                <div class="option-description">Anime movies & series</div>
                            </div>
                            <div class="option-card" onclick="selectOption('hentai')">
                                <div class="option-title">Hentai</div>
                                <div class="option-description">Adult anime content</div>
                            </div>
                        </div>
                        <input type="hidden" name="type" id="selectedType" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="limit">Import Limit:</label>
                        <select name="limit" id="limit">
                            <option value="25">25 items</option>
                            <option value="50" selected>50 items</option>
                            <option value="100">100 items</option>
                            <option value="200">200 items</option>
                        </select>
                    </div>
                    
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" id="importBtn">Start Import</button>
                </form>
            </div>

            <!-- Custom JSON Import -->
            <div class="import-section">
                <h2 class="section-title">Custom JSON Import</h2>
                <p class="section-description">
                    Import content from custom JSON files. The JSON should contain an array of objects with TMDB IDs.
                </p>
                
                <form method="POST">
                    <input type="hidden" name="action" value="import_custom_json">
                    
                    <div class="form-group">
                        <label for="json_url">JSON URL:</label>
                        <input type="url" name="json_url" id="json_url" 
                               placeholder="https://example.com/content.json" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="content_type">Content Type:</label>
                        <select name="content_type" id="content_type" required>
                            <option value="">Select type...</option>
                            <option value="movie">Movies</option>
                            <option value="tv_show">TV Shows</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Import from JSON</button>
                </form>
                
                <div style="margin-top: 20px; padding: 15px; background: var(--dark-bg); border-radius: 6px;">
                    <h4 style="color: var(--text-primary); margin-bottom: 10px;">Expected JSON Format:</h4>
                    <pre style="color: var(--text-secondary); font-size: 0.9rem; overflow-x: auto;">
[
  {"tmdb_id": 550, "title": "Fight Club"},
  {"tmdb_id": 13, "title": "Forrest Gump"},
  ...
]</pre>
                </div>
            </div>

            <!-- TMDB Direct Import -->
            <div class="import-section">
                <h2 class="section-title">TMDB Direct Import</h2>
                <p class="section-description">
                    Import individual content by TMDB ID or use TMDB's curated lists.
                </p>
                
                <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                    <button class="btn btn-secondary" onclick="bulkImport('trending_movies')">Trending Movies</button>
                    <button class="btn btn-secondary" onclick="bulkImport('popular_movies')">Popular Movies</button>
                </div>
                
                <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                    <button class="btn btn-secondary" onclick="bulkImport('trending_tv')">Trending TV</button>
                    <button class="btn btn-secondary" onclick="bulkImport('popular_tv')">Popular TV</button>
                </div>
                
                <div style="display: flex; gap: 15px;">
                    <button class="btn btn-secondary" onclick="bulkImport('now_playing')">Now Playing</button>
                    <button class="btn btn-secondary" onclick="bulkImport('upcoming')">Upcoming</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedType = '';
        
        function selectOption(type) {
            // Remove previous selection
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selection to clicked card
            event.target.closest('.option-card').classList.add('selected');
            
            // Set hidden input value
            document.getElementById('selectedType').value = type;
            selectedType = type;
        }
        
        document.getElementById('letsembedForm').addEventListener('submit', function(e) {
            if (!selectedType) {
                e.preventDefault();
                alert('Please select a content type first.');
                return;
            }

            const btn = document.getElementById('importBtn');
            const progressBar = document.getElementById('progressBar');

            btn.disabled = true;
            btn.textContent = 'Importing...';
            progressBar.style.display = 'block';

            // Simulate progress (since we can't track real progress)
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 5;
                if (progress > 85) progress = 85;
                document.getElementById('progressFill').style.width = progress + '%';
            }, 1000);

            // Clean up on form submission
            setTimeout(() => {
                clearInterval(interval);
                document.getElementById('progressFill').style.width = '100%';
            }, 5000);
        });
        
        async function bulkImport(type) {
            if (!confirm(`Import ${type.replace('_', ' ')}? This may take a while.`)) {
                return;
            }
            
            try {
                const response = await fetch('../api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_' + type,
                        type: type.includes('tv') ? 'tv' : 'movie'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Successfully imported ${data.count} items`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
