import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';

import 'core/constants/app_constants.dart';
import 'core/theme/app_theme.dart';
import 'core/services/api_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/auth_service.dart';
import 'core/utils/device_utils.dart';
import 'features/splash/splash_screen.dart';
import 'features/auth/providers/auth_provider.dart';
import 'features/home/<USER>/home_provider.dart';
import 'features/movies/providers/movies_provider.dart';
import 'features/tv_shows/providers/tv_shows_provider.dart';
import 'features/player/providers/player_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Hive
  await Hive.initFlutter();
  
  // Initialize Services
  await StorageService.init();
  await ApiService.init();
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: Colors.black,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );
  
  runApp(const StreamFlixApp());
}

class StreamFlixApp extends StatelessWidget {
  const StreamFlixApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => HomeProvider()),
        ChangeNotifierProvider(create: (_) => MoviesProvider()),
        ChangeNotifierProvider(create: (_) => TvShowsProvider()),
        ChangeNotifierProvider(create: (_) => PlayerProvider()),
      ],
      child: GetMaterialApp(
        title: AppConstants.appName,
        debugShowCheckedModeBanner: false,
        theme: AppTheme.darkTheme,
        home: const SplashScreen(),
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaler: TextScaler.linear(
                DeviceUtils.isTV(context) ? 1.2 : 1.0,
              ),
            ),
            child: child!,
          );
        },
      ),
    );
  }
}

// App Constants
class AppConstants {
  static const String appName = 'StreamFlix';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'http://your-domain.com'; // Replace with your domain
  static const String apiVersion = 'v1';
  static const String apiUrl = '$baseUrl/api/$apiVersion';
  
  // TMDB Configuration
  static const String tmdbImageBaseUrl = 'https://image.tmdb.org/t/p/';
  static const String tmdbImageSize = 'w500';
  static const String tmdbBackdropSize = 'w1280';
  static const String tmdbOriginalSize = 'original';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String watchlistKey = 'watchlist';
  static const String watchHistoryKey = 'watch_history';
  static const String settingsKey = 'app_settings';
  
  // Player Configuration
  static const Duration seekDuration = Duration(seconds: 10);
  static const Duration bufferDuration = Duration(seconds: 30);
  static const double defaultVolume = 1.0;
  
  // UI Configuration
  static const double cardAspectRatio = 2 / 3;
  static const double backdropAspectRatio = 16 / 9;
  static const int gridCrossAxisCount = 2;
  static const int tvGridCrossAxisCount = 6;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Network Configuration
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  
  // Content Types
  static const String movieType = 'movie';
  static const String tvShowType = 'tv_show';
  static const String animeType = 'anime';
  static const String hentaiType = 'hentai';
  
  // Quality Options
  static const List<String> qualityOptions = [
    '480p',
    '720p',
    '1080p',
    'Auto'
  ];
  
  // Subtitle Options
  static const List<String> subtitleOptions = [
    'None',
    'English',
    'Spanish',
    'French',
    'German',
    'Auto'
  ];
}

// Device Utils
class DeviceUtils {
  static bool isTV(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final diagonal = (size.width * size.width + size.height * size.height);
    return diagonal > 1000000; // Rough TV detection
  }
  
  static bool isTablet(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final diagonal = (size.width * size.width + size.height * size.height);
    return diagonal > 550000 && diagonal <= 1000000;
  }
  
  static bool isMobile(BuildContext context) {
    return !isTV(context) && !isTablet(context);
  }
  
  static int getGridCrossAxisCount(BuildContext context) {
    if (isTV(context)) return AppConstants.tvGridCrossAxisCount;
    if (isTablet(context)) return 4;
    return AppConstants.gridCrossAxisCount;
  }
  
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = getGridCrossAxisCount(context);
    final spacing = 16.0 * (crossAxisCount - 1);
    final padding = 32.0; // Left and right padding
    return (screenWidth - spacing - padding) / crossAxisCount;
  }
  
  static double getCardHeight(BuildContext context) {
    return getCardWidth(context) / AppConstants.cardAspectRatio;
  }
}

// Error Handler
class AppErrorHandler {
  static void handleError(dynamic error, {String? context}) {
    debugPrint('Error in $context: $error');
    
    // Log to crash analytics in production
    // FirebaseCrashlytics.instance.recordError(error, null);
    
    // Show user-friendly error message
    if (Get.context != null) {
      Get.snackbar(
        'Error',
        _getErrorMessage(error),
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    }
  }
  
  static String _getErrorMessage(dynamic error) {
    if (error.toString().contains('SocketException')) {
      return 'No internet connection';
    } else if (error.toString().contains('TimeoutException')) {
      return 'Request timeout. Please try again.';
    } else if (error.toString().contains('FormatException')) {
      return 'Invalid data format';
    } else {
      return 'Something went wrong. Please try again.';
    }
  }
}

// App Lifecycle Handler
class AppLifecycleHandler extends WidgetsBindingObserver {
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        // App is in foreground
        break;
      case AppLifecycleState.paused:
        // App is in background
        break;
      case AppLifecycleState.detached:
        // App is being terminated
        break;
      case AppLifecycleState.inactive:
        // App is inactive
        break;
      case AppLifecycleState.hidden:
        // App is hidden
        break;
    }
  }
}
