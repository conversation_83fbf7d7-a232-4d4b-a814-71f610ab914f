import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';

class HomeProvider extends ChangeNotifier {
  // Loading states
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;

  // Content data
  Map<String, dynamic>? _heroContent;
  List<Map<String, dynamic>> _featuredMovies = [];
  List<Map<String, dynamic>> _trendingMovies = [];
  List<Map<String, dynamic>> _featuredTvShows = [];
  List<Map<String, dynamic>> _trendingTvShows = [];
  List<Map<String, dynamic>> _continueWatching = [];
  List<Map<String, dynamic>> _watchlist = [];
  List<Map<String, dynamic>> _recentlyAdded = [];

  // Getters
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  
  Map<String, dynamic>? get heroContent => _heroContent;
  List<Map<String, dynamic>> get featuredMovies => _featuredMovies;
  List<Map<String, dynamic>> get trendingMovies => _trendingMovies;
  List<Map<String, dynamic>> get featuredTvShows => _featuredTvShows;
  List<Map<String, dynamic>> get trendingTvShows => _trendingTvShows;
  List<Map<String, dynamic>> get continueWatching => _continueWatching;
  List<Map<String, dynamic>> get watchlist => _watchlist;
  List<Map<String, dynamic>> get recentlyAdded => _recentlyAdded;

  // Load all home data
  Future<void> loadHomeData() async {
    _setLoading(true);
    _setError(false);

    try {
      await Future.wait([
        _loadFeaturedMovies(),
        _loadTrendingMovies(),
        _loadFeaturedTvShows(),
        _loadTrendingTvShows(),
        _loadContinueWatching(),
        _loadWatchlist(),
        _loadRecentlyAdded(),
      ]);

      _setHeroContent();
      _setLoading(false);
    } catch (e) {
      _setError(true, e.toString());
      _setLoading(false);
    }
  }

  // Load featured movies
  Future<void> _loadFeaturedMovies() async {
    try {
      final response = await ApiService.getMovies(filter: 'featured');
      if (response.success && response.data != null) {
        _featuredMovies = List<Map<String, dynamic>>.from(
          response.data['movies'] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error loading featured movies: $e');
    }
  }

  // Load trending movies
  Future<void> _loadTrendingMovies() async {
    try {
      final response = await ApiService.getMovies(filter: 'trending');
      if (response.success && response.data != null) {
        _trendingMovies = List<Map<String, dynamic>>.from(
          response.data['movies'] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error loading trending movies: $e');
    }
  }

  // Load featured TV shows
  Future<void> _loadFeaturedTvShows() async {
    try {
      final response = await ApiService.getTvShows(filter: 'featured');
      if (response.success && response.data != null) {
        _featuredTvShows = List<Map<String, dynamic>>.from(
          response.data['tv_shows'] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error loading featured TV shows: $e');
    }
  }

  // Load trending TV shows
  Future<void> _loadTrendingTvShows() async {
    try {
      final response = await ApiService.getTvShows(filter: 'trending');
      if (response.success && response.data != null) {
        _trendingTvShows = List<Map<String, dynamic>>.from(
          response.data['tv_shows'] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error loading trending TV shows: $e');
    }
  }

  // Load continue watching from local storage
  Future<void> _loadContinueWatching() async {
    try {
      final history = StorageService.getWatchHistory();
      _continueWatching = history.take(10).toList();
    } catch (e) {
      debugPrint('Error loading continue watching: $e');
    }
  }

  // Load watchlist from local storage
  Future<void> _loadWatchlist() async {
    try {
      final watchlistData = StorageService.getWatchlist();
      _watchlist = watchlistData.take(10).toList();
    } catch (e) {
      debugPrint('Error loading watchlist: $e');
    }
  }

  // Load recently added content
  Future<void> _loadRecentlyAdded() async {
    try {
      // Combine recent movies and TV shows
      final moviesResponse = await ApiService.getMovies();
      final tvShowsResponse = await ApiService.getTvShows();
      
      List<Map<String, dynamic>> recentContent = [];
      
      if (moviesResponse.success && moviesResponse.data != null) {
        final movies = List<Map<String, dynamic>>.from(
          moviesResponse.data['movies'] ?? []
        );
        recentContent.addAll(movies.take(5));
      }
      
      if (tvShowsResponse.success && tvShowsResponse.data != null) {
        final tvShows = List<Map<String, dynamic>>.from(
          tvShowsResponse.data['tv_shows'] ?? []
        );
        recentContent.addAll(tvShows.take(5));
      }
      
      // Sort by created_at date
      recentContent.sort((a, b) {
        final dateA = DateTime.tryParse(a['created_at'] ?? '') ?? DateTime.now();
        final dateB = DateTime.tryParse(b['created_at'] ?? '') ?? DateTime.now();
        return dateB.compareTo(dateA);
      });
      
      _recentlyAdded = recentContent.take(10).toList();
    } catch (e) {
      debugPrint('Error loading recently added: $e');
    }
  }

  // Set hero content from featured content
  void _setHeroContent() {
    final allFeatured = [
      ..._featuredMovies,
      ..._featuredTvShows,
    ];
    
    if (allFeatured.isNotEmpty) {
      // Pick a random featured content for hero
      allFeatured.shuffle();
      _heroContent = allFeatured.first;
    }
  }

  // Refresh specific section
  Future<void> refreshSection(String section) async {
    switch (section) {
      case 'featured_movies':
        await _loadFeaturedMovies();
        break;
      case 'trending_movies':
        await _loadTrendingMovies();
        break;
      case 'featured_tv':
        await _loadFeaturedTvShows();
        break;
      case 'trending_tv':
        await _loadTrendingTvShows();
        break;
      case 'continue_watching':
        await _loadContinueWatching();
        break;
      case 'watchlist':
        await _loadWatchlist();
        break;
      case 'recently_added':
        await _loadRecentlyAdded();
        break;
    }
    notifyListeners();
  }

  // Add to watchlist
  Future<void> addToWatchlist(Map<String, dynamic> content) async {
    try {
      await StorageService.addToWatchlist(content);
      await _loadWatchlist();
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding to watchlist: $e');
    }
  }

  // Remove from watchlist
  Future<void> removeFromWatchlist(int tmdbId, String contentType) async {
    try {
      await StorageService.removeFromWatchlist(tmdbId, contentType);
      await _loadWatchlist();
      notifyListeners();
    } catch (e) {
      debugPrint('Error removing from watchlist: $e');
    }
  }

  // Add to watch history
  Future<void> addToWatchHistory(Map<String, dynamic> content) async {
    try {
      await StorageService.addToWatchHistory(content);
      await _loadContinueWatching();
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding to watch history: $e');
    }
  }

  // Check if content is in watchlist
  bool isInWatchlist(int tmdbId, String contentType) {
    return StorageService.isInWatchlist(tmdbId, contentType);
  }

  // Get content by type and filter
  Future<List<Map<String, dynamic>>> getContentByFilter(
    String contentType,
    String filter, {
    int page = 1,
  }) async {
    try {
      ApiResponse response;
      
      if (contentType == 'movie') {
        response = await ApiService.getMovies(filter: filter, page: page);
      } else {
        response = await ApiService.getTvShows(filter: filter, page: page);
      }
      
      if (response.success && response.data != null) {
        final key = contentType == 'movie' ? 'movies' : 'tv_shows';
        return List<Map<String, dynamic>>.from(
          response.data[key] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error getting content by filter: $e');
    }
    
    return [];
  }

  // Search content
  Future<List<Map<String, dynamic>>> searchContent(
    String query, {
    int page = 1,
  }) async {
    try {
      final response = await ApiService.search(query, page: page);
      
      if (response.success && response.data != null) {
        return List<Map<String, dynamic>>.from(
          response.data['results'] ?? []
        );
      }
    } catch (e) {
      debugPrint('Error searching content: $e');
    }
    
    return [];
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(bool error, [String? message]) {
    _hasError = error;
    _errorMessage = message;
    notifyListeners();
  }

  // Clear all data
  void clearData() {
    _heroContent = null;
    _featuredMovies.clear();
    _trendingMovies.clear();
    _featuredTvShows.clear();
    _trendingTvShows.clear();
    _continueWatching.clear();
    _watchlist.clear();
    _recentlyAdded.clear();
    notifyListeners();
  }
}
