# 🎬 StreamFlix Mobile & TV App

A modern Flutter-based streaming application for Android mobile and TV platforms, featuring a Netflix-style interface with comprehensive video streaming capabilities.

## 📱 Features

### 🎯 Core Features
- **Cross-Platform**: Single codebase for mobile and Android TV
- **Modern UI**: Netflix-inspired dark theme with Material Design 3
- **Video Streaming**: Multiple server support with quality selection
- **Content Management**: Movies, TV shows, anime, and hentai content
- **User Authentication**: Login/register with guest mode
- **Watchlist**: Personal content collection
- **Continue Watching**: Resume playback from where you left off
- **Search**: Advanced search with filters and suggestions

### 📱 Mobile Features
- Touch-optimized navigation
- Swipe gestures
- Pull-to-refresh
- Portrait/landscape orientation
- Responsive grid layouts
- Bottom navigation

### 📺 TV Features
- D-pad navigation with focus indicators
- Remote control support
- Leanback UI optimized for 10-foot experience
- Large text and buttons
- Side navigation for TV
- Voice search integration (planned)

### 🎬 Player Features
- Multiple embed servers
- Quality selection (480p, 720p, 1080p, Auto)
- Subtitle support
- Volume and brightness control
- Seek forward/backward
- Fullscreen mode
- Resume playback
- Watch progress tracking

## 🏗️ Architecture

### 📦 Project Structure
```
mobile_app/
├── lib/
│   ├── core/
│   │   ├── theme/           # App theming
│   │   ├── services/        # API, Storage, Auth services
│   │   ├── utils/           # Utilities and helpers
│   │   └── constants/       # App constants
│   ├── features/
│   │   ├── splash/          # Splash screen
│   │   ├── auth/            # Authentication
│   │   ├── home/            # Home screen
│   │   ├── movies/          # Movies section
│   │   ├── tv_shows/        # TV shows section
│   │   ├── search/          # Search functionality
│   │   ├── player/          # Video player
│   │   ├── profile/         # User profile
│   │   └── main/            # Main navigation
│   └── main.dart
├── android/
│   ├── app/
│   │   ├── src/main/
│   │   │   ├── AndroidManifest.xml
│   │   │   └── kotlin/
│   │   └── build.gradle
│   └── build.gradle
├── build_scripts/
│   └── build_all.sh         # Build automation script
└── pubspec.yaml
```

### 🎨 Design System
- **Colors**: Netflix-inspired red and dark theme
- **Typography**: Inter font family
- **Components**: Material Design 3 components
- **Responsive**: Adaptive layouts for mobile/tablet/TV
- **Animations**: Smooth transitions and focus effects

### 🔧 State Management
- **Provider Pattern**: For state management
- **Local Storage**: Hive for offline data
- **API Integration**: Dio for HTTP requests
- **Navigation**: GetX for routing

## 🚀 Getting Started

### 📋 Prerequisites
- Flutter SDK (>=3.10.0)
- Android Studio / VS Code
- Android SDK (API level 21+)
- Git

### 🛠️ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd mobile_app
```

2. **Install dependencies**
```bash
flutter pub get
```

3. **Configure API endpoint**
```dart
// lib/core/constants/app_constants.dart
static const String baseUrl = 'http://your-domain.com';
```

4. **Run the app**
```bash
# Mobile
flutter run

# TV (with Android TV emulator)
flutter run -d android_tv
```

### 🏗️ Building

#### Quick Build (All Variants)
```bash
chmod +x build_scripts/build_all.sh
./build_scripts/build_all.sh
```

#### Manual Build Commands
```bash
# Mobile APK
flutter build apk --flavor mobile --release

# TV APK  
flutter build apk --flavor tv --release

# App Bundle for Play Store
flutter build appbundle --flavor mobile --release
flutter build appbundle --flavor tv --release
```

#### Build Options
```bash
# Mobile only
./build_scripts/build_all.sh --mobile-only

# TV only
./build_scripts/build_all.sh --tv-only

# App bundles only
./build_scripts/build_all.sh --bundle-only
```

## 📱 Installation

### Mobile Installation
```bash
adb install outputs/streamflix-mobile-release.apk
```

### TV Installation
```bash
adb install outputs/streamflix-tv-release.apk
```

### Sideloading on Android TV
1. Enable Developer Options on your Android TV
2. Enable USB Debugging
3. Connect via ADB or use file manager
4. Install the TV APK

## 🎮 TV Remote Controls

### Navigation
- **D-Pad**: Navigate between UI elements
- **Select/Enter**: Activate focused element
- **Back**: Go back or exit
- **Home**: Return to home screen
- **Menu**: Show context menu

### Player Controls
- **Play/Pause**: Toggle playback
- **Fast Forward**: Seek forward 10 seconds
- **Rewind**: Seek backward 10 seconds
- **Volume Up/Down**: Adjust volume
- **Mute**: Toggle mute

## 🔧 Configuration

### API Configuration
```dart
// lib/core/constants/app_constants.dart
class AppConstants {
  static const String baseUrl = 'http://your-domain.com';
  static const String apiVersion = 'v1';
  static const String apiUrl = '$baseUrl/api/$apiVersion';
}
```

### Build Configuration
```gradle
// android/app/build.gradle
android {
    defaultConfig {
        applicationId "com.streamflix.app"
        minSdk 21
        targetSdk 34
    }
}
```

## 📦 Dependencies

### Core Dependencies
- `flutter`: UI framework
- `provider`: State management
- `get`: Navigation and utilities
- `dio`: HTTP client
- `hive`: Local storage

### Media Dependencies
- `video_player`: Video playback
- `chewie`: Video player UI
- `wakelock`: Keep screen on during playback

### UI Dependencies
- `cached_network_image`: Image caching
- `shimmer`: Loading animations
- `flutter_staggered_grid_view`: Grid layouts

### TV Dependencies
- `flutter_tv`: TV-specific utilities

## 🎯 Features Roadmap

### ✅ Completed
- [x] Cross-platform mobile/TV support
- [x] Video player with multiple servers
- [x] User authentication
- [x] Content browsing and search
- [x] Watchlist and watch history
- [x] TV remote navigation
- [x] Responsive design

### 🚧 In Progress
- [ ] Offline downloads
- [ ] Chromecast support
- [ ] Push notifications
- [ ] Biometric authentication

### 📋 Planned
- [ ] Voice search for TV
- [ ] Picture-in-picture mode
- [ ] Parental controls
- [ ] Multiple user profiles
- [ ] Content recommendations
- [ ] Social features (ratings, reviews)

## 🐛 Troubleshooting

### Common Issues

**Build Errors**
```bash
flutter clean
flutter pub get
flutter build apk
```

**TV Navigation Issues**
- Ensure focus nodes are properly configured
- Check TV remote key mappings
- Verify leanback support in manifest

**Video Playback Issues**
- Check network connectivity
- Verify server URLs
- Test with different video formats

**Performance Issues**
- Enable release mode for testing
- Check memory usage
- Optimize image loading

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on both mobile and TV
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation

---

**Built with ❤️ using Flutter for StreamFlix**
