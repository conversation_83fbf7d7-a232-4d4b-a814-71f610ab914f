import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock/wakelock.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/device_utils.dart';
import '../providers/player_provider.dart';
import '../widgets/player_controls.dart';
import '../widgets/quality_selector.dart';
import '../widgets/subtitle_selector.dart';
import '../widgets/server_selector.dart';

class PlayerScreen extends StatefulWidget {
  final int tmdbId;
  final String contentType;
  final String title;
  final int? season;
  final int? episode;

  const PlayerScreen({
    super.key,
    required this.tmdbId,
    required this.contentType,
    required this.title,
    this.season,
    this.episode,
  });

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen>
    with TickerProviderStateMixin {
  late PlayerProvider _playerProvider;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;
  
  bool _showControls = true;
  bool _isFullscreen = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initPlayer();
    _enableWakelock();
  }

  void _initAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _controlsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controlsAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _controlsAnimationController.forward();
  }

  void _initPlayer() {
    _playerProvider = context.read<PlayerProvider>();
    _playerProvider.initializePlayer(
      tmdbId: widget.tmdbId,
      contentType: widget.contentType,
      season: widget.season,
      episode: widget.episode,
    );
  }

  void _enableWakelock() {
    Wakelock.enable();
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _playerProvider.dispose();
    Wakelock.disable();
    _exitFullscreen();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Consumer<PlayerProvider>(
        builder: (context, provider, child) {
          return Stack(
            children: [
              // Video Player
              _buildVideoPlayer(provider),
              
              // Loading Indicator
              if (provider.isLoading)
                _buildLoadingIndicator(),
              
              // Error State
              if (provider.hasError)
                _buildErrorState(provider),
              
              // Player Controls
              if (!provider.isLoading && !provider.hasError)
                _buildPlayerControls(provider),
              
              // Back Button (when not fullscreen)
              if (!_isFullscreen)
                _buildBackButton(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildVideoPlayer(PlayerProvider provider) {
    if (provider.videoController == null) {
      return const SizedBox.expand();
    }

    return GestureDetector(
      onTap: _toggleControls,
      child: Center(
        child: AspectRatio(
          aspectRatio: provider.videoController!.value.aspectRatio,
          child: VideoPlayer(provider.videoController!),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: AppTheme.primaryColor,
              strokeWidth: 3,
            ),
            SizedBox(height: 16),
            Text(
              'Loading video...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(PlayerProvider provider) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: AppTheme.errorColor,
                size: 64,
              ),
              const SizedBox(height: 16),
              const Text(
                'Playback Error',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                provider.errorMessage ?? 'Unable to play video',
                style: const TextStyle(
                  color: AppTheme.textSecondary,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () => provider.retryPlayback(),
                    child: const Text('Retry'),
                  ),
                  const SizedBox(width: 16),
                  if (provider.availableServers.length > 1)
                    ElevatedButton(
                      onPressed: () => _showServerSelector(provider),
                      child: const Text('Try Another Server'),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlayerControls(PlayerProvider provider) {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _showControls ? _controlsAnimation.value : 0.0,
          child: PlayerControls(
            provider: provider,
            onPlayPause: provider.togglePlayPause,
            onSeek: provider.seekTo,
            onVolumeChange: provider.setVolume,
            onBrightnessChange: _setBrightness,
            onFullscreenToggle: _toggleFullscreen,
            onQualityTap: () => _showQualitySelector(provider),
            onSubtitleTap: () => _showSubtitleSelector(provider),
            onServerTap: () => _showServerSelector(provider),
            isFullscreen: _isFullscreen,
            title: widget.title,
          ),
        );
      },
    );
  }

  Widget _buildBackButton() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 8,
      left: 16,
      child: AnimatedBuilder(
        animation: _controlsAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _showControls ? _controlsAnimation.value : 0.0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back,
                  color: Colors.white,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
          );
        },
      ),
    );
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _controlsAnimationController.forward();
      _hideControlsAfterDelay();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
    
    if (_isFullscreen) {
      _enterFullscreen();
    } else {
      _exitFullscreen();
    }
  }

  void _enterFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  void _setBrightness(double brightness) {
    // Implement brightness control
  }

  void _showQualitySelector(PlayerProvider provider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.cardBg,
      builder: (context) => QualitySelector(
        currentQuality: provider.currentQuality,
        availableQualities: provider.availableQualities,
        onQualitySelected: (quality) {
          provider.changeQuality(quality);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showSubtitleSelector(PlayerProvider provider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.cardBg,
      builder: (context) => SubtitleSelector(
        currentSubtitle: provider.currentSubtitle,
        availableSubtitles: provider.availableSubtitles,
        onSubtitleSelected: (subtitle) {
          provider.changeSubtitle(subtitle);
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showServerSelector(PlayerProvider provider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.cardBg,
      builder: (context) => ServerSelector(
        currentServer: provider.currentServer,
        availableServers: provider.availableServers,
        onServerSelected: (server) {
          provider.changeServer(server);
          Navigator.pop(context);
        },
      ),
    );
  }
}
