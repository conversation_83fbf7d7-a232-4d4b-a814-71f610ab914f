<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Handle actions
if ($_POST['action'] ?? '' === 'toggle_featured') {
    $movie_id = (int)($_POST['movie_id'] ?? 0);
    if ($movie_id > 0) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("UPDATE movies SET is_featured = NOT is_featured WHERE id = :id");
            $stmt->execute([':id' => $movie_id]);
            $message = 'Movie featured status updated!';
        } catch (Exception $e) {
            $error = 'Failed to update movie: ' . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'toggle_trending') {
    $movie_id = (int)($_POST['movie_id'] ?? 0);
    if ($movie_id > 0) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("UPDATE movies SET is_trending = NOT is_trending WHERE id = :id");
            $stmt->execute([':id' => $movie_id]);
            $message = 'Movie trending status updated!';
        } catch (Exception $e) {
            $error = 'Failed to update movie: ' . $e->getMessage();
        }
    }
}

if ($_POST['action'] ?? '' === 'delete_movie') {
    $movie_id = (int)($_POST['movie_id'] ?? 0);
    if ($movie_id > 0) {
        try {
            $db = new Database();
            $conn = $db->connect();
            $stmt = $conn->prepare("DELETE FROM movies WHERE id = :id");
            $stmt->execute([':id' => $movie_id]);
            $message = 'Movie deleted successfully!';
        } catch (Exception $e) {
            $error = 'Failed to delete movie: ' . $e->getMessage();
        }
    }
}

try {
    $db = new Database();
    $conn = $db->connect();
    
    // Get movies with pagination
    $stmt = $conn->prepare("
        SELECT m.*, COUNT(*) OVER() as total_count
        FROM movies m 
        ORDER BY m.created_at DESC 
        LIMIT :limit OFFSET :offset
    ");
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $movies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_count = $movies[0]['total_count'] ?? 0;
    $total_pages = ceil($total_count / $limit);
    
} catch (Exception $e) {
    $movies = [];
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Movies - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .movies-table {
            background: var(--secondary-color);
            border-radius: 8px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .table th {
            background: var(--dark-bg);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .table td {
            color: var(--text-secondary);
        }
        
        .movie-poster {
            width: 60px;
            height: 90px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .movie-title {
            color: var(--text-primary);
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-featured {
            background: var(--primary-color);
            color: white;
        }
        
        .badge-trending {
            background: #ffd700;
            color: #000;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .btn-toggle {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .btn-toggle:hover {
            background: var(--primary-color);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
        }
        
        .pagination a,
        .pagination span {
            padding: 10px 15px;
            background: var(--secondary-color);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover {
            background: var(--primary-color);
        }
        
        .pagination .current {
            background: var(--primary-color);
        }
        
        .bulk-actions {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .bulk-actions h3 {
            margin-bottom: 15px;
            color: var(--text-primary);
        }
        
        .bulk-btn {
            margin: 5px;
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Manage Movies</h1>
            <p>View and manage all movies in your database</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php" class="active">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Bulk Actions -->
        <div class="bulk-actions">
            <h3>Bulk Import</h3>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('trending_movies')">Import Trending Movies</button>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('popular_movies')">Import Popular Movies</button>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('now_playing')">Import Now Playing</button>
            <button class="btn btn-primary bulk-btn" onclick="bulkImport('upcoming')">Import Upcoming</button>
        </div>

        <!-- Movies Table -->
        <div class="movies-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Poster</th>
                        <th>Title</th>
                        <th>Year</th>
                        <th>Rating</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($movies as $movie): ?>
                    <tr>
                        <td>
                            <img src="<?php echo getImageUrl($movie['poster_path'], 'w200'); ?>" 
                                 alt="<?php echo htmlspecialchars($movie['title']); ?>" 
                                 class="movie-poster">
                        </td>
                        <td>
                            <div class="movie-title"><?php echo htmlspecialchars($movie['title']); ?></div>
                            <small>TMDB ID: <?php echo $movie['tmdb_id']; ?></small>
                        </td>
                        <td><?php echo date('Y', strtotime($movie['release_date'])); ?></td>
                        <td>★ <?php echo $movie['vote_average']; ?></td>
                        <td>
                            <?php if ($movie['is_featured']): ?>
                                <span class="status-badge badge-featured">Featured</span>
                            <?php endif; ?>
                            <?php if ($movie['is_trending']): ?>
                                <span class="status-badge badge-trending">Trending</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_featured">
                                <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                                <button type="submit" class="action-btn btn-toggle">
                                    <?php echo $movie['is_featured'] ? 'Unfeature' : 'Feature'; ?>
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_trending">
                                <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                                <button type="submit" class="action-btn btn-toggle">
                                    <?php echo $movie['is_trending'] ? 'Untrend' : 'Trend'; ?>
                                </button>
                            </form>
                            
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this movie?')">
                                <input type="hidden" name="action" value="delete_movie">
                                <input type="hidden" name="movie_id" value="<?php echo $movie['id']; ?>">
                                <button type="submit" class="action-btn btn-delete">Delete</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>">← Previous</a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>">Next →</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        async function bulkImport(type) {
            if (!confirm(`Import ${type.replace('_', ' ')}? This may take a while.`)) {
                return;
            }
            
            try {
                const response = await fetch('../api/bulk-import.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'import_' + type,
                        type: 'movie'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`Successfully imported ${data.count} movies`);
                    location.reload();
                } else {
                    alert('Import failed: ' + data.message);
                }
            } catch (error) {
                alert('Import failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
