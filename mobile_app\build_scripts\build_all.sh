#!/bin/bash

# StreamFlix Mobile & TV App Build Script
# This script builds both mobile and TV versions of the app

set -e

echo "🎬 StreamFlix Build Script"
echo "=========================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_step() {
    echo -e "${BLUE}📱 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Flutter is installed
check_flutter() {
    print_step "Checking Flutter installation..."
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    flutter --version
    print_success "Flutter is installed"
}

# Clean previous builds
clean_build() {
    print_step "Cleaning previous builds..."
    flutter clean
    flutter pub get
    print_success "Project cleaned"
}

# Build Mobile APK
build_mobile_apk() {
    print_step "Building Mobile APK..."
    
    # Debug APK
    print_step "Building debug APK..."
    flutter build apk --flavor mobile --debug
    
    # Release APK
    print_step "Building release APK..."
    flutter build apk --flavor mobile --release
    
    print_success "Mobile APK built successfully"
}

# Build TV APK
build_tv_apk() {
    print_step "Building TV APK..."
    
    # Debug APK
    print_step "Building TV debug APK..."
    flutter build apk --flavor tv --debug
    
    # Release APK
    print_step "Building TV release APK..."
    flutter build apk --flavor tv --release
    
    print_success "TV APK built successfully"
}

# Build App Bundle for Play Store
build_app_bundle() {
    print_step "Building App Bundle for Play Store..."
    
    # Mobile Bundle
    flutter build appbundle --flavor mobile --release
    
    # TV Bundle
    flutter build appbundle --flavor tv --release
    
    print_success "App Bundles built successfully"
}

# Copy APKs to output directory
copy_outputs() {
    print_step "Copying build outputs..."
    
    # Create output directory
    mkdir -p outputs
    
    # Copy Mobile APKs
    if [ -f "build/app/outputs/flutter-apk/app-mobile-debug.apk" ]; then
        cp build/app/outputs/flutter-apk/app-mobile-debug.apk outputs/streamflix-mobile-debug.apk
    fi
    
    if [ -f "build/app/outputs/flutter-apk/app-mobile-release.apk" ]; then
        cp build/app/outputs/flutter-apk/app-mobile-release.apk outputs/streamflix-mobile-release.apk
    fi
    
    # Copy TV APKs
    if [ -f "build/app/outputs/flutter-apk/app-tv-debug.apk" ]; then
        cp build/app/outputs/flutter-apk/app-tv-debug.apk outputs/streamflix-tv-debug.apk
    fi
    
    if [ -f "build/app/outputs/flutter-apk/app-tv-release.apk" ]; then
        cp build/app/outputs/flutter-apk/app-tv-release.apk outputs/streamflix-tv-release.apk
    fi
    
    # Copy App Bundles
    if [ -f "build/app/outputs/bundle/mobileRelease/app-mobile-release.aab" ]; then
        cp build/app/outputs/bundle/mobileRelease/app-mobile-release.aab outputs/streamflix-mobile-release.aab
    fi
    
    if [ -f "build/app/outputs/bundle/tvRelease/app-tv-release.aab" ]; then
        cp build/app/outputs/bundle/tvRelease/app-tv-release.aab outputs/streamflix-tv-release.aab
    fi
    
    print_success "Build outputs copied to outputs/ directory"
}

# Show build summary
show_summary() {
    print_step "Build Summary"
    echo "============="
    
    if [ -d "outputs" ]; then
        echo "📱 Mobile APKs:"
        ls -la outputs/streamflix-mobile-*.apk 2>/dev/null || echo "  No mobile APKs found"
        
        echo ""
        echo "📺 TV APKs:"
        ls -la outputs/streamflix-tv-*.apk 2>/dev/null || echo "  No TV APKs found"
        
        echo ""
        echo "📦 App Bundles:"
        ls -la outputs/streamflix-*.aab 2>/dev/null || echo "  No app bundles found"
        
        echo ""
        echo "🎯 Installation Commands:"
        echo "  Mobile Debug:   adb install outputs/streamflix-mobile-debug.apk"
        echo "  Mobile Release: adb install outputs/streamflix-mobile-release.apk"
        echo "  TV Debug:       adb install outputs/streamflix-tv-debug.apk"
        echo "  TV Release:     adb install outputs/streamflix-tv-release.apk"
    fi
    
    print_success "Build completed successfully! 🎉"
}

# Main execution
main() {
    # Parse command line arguments
    BUILD_TYPE="all"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mobile-only)
                BUILD_TYPE="mobile"
                shift
                ;;
            --tv-only)
                BUILD_TYPE="tv"
                shift
                ;;
            --bundle-only)
                BUILD_TYPE="bundle"
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --mobile-only   Build only mobile APK"
                echo "  --tv-only       Build only TV APK"
                echo "  --bundle-only   Build only app bundles"
                echo "  --help          Show this help message"
                exit 0
                ;;
            *)
                print_warning "Unknown option: $1"
                shift
                ;;
        esac
    done
    
    # Execute build steps
    check_flutter
    clean_build
    
    case $BUILD_TYPE in
        "mobile")
            build_mobile_apk
            ;;
        "tv")
            build_tv_apk
            ;;
        "bundle")
            build_app_bundle
            ;;
        "all")
            build_mobile_apk
            build_tv_apk
            build_app_bundle
            ;;
    esac
    
    copy_outputs
    show_summary
}

# Run main function
main "$@"
