import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TvNavigationHelper {
  static const double focusScale = 1.1;
  static const Duration focusAnimationDuration = Duration(milliseconds: 200);
  
  // TV Remote Key Mappings
  static const Map<LogicalKeyboardKey, TvRemoteKey> keyMappings = {
    LogicalKeyboardKey.arrowUp: TvRemoteKey.up,
    LogicalKeyboardKey.arrowDown: TvRemoteKey.down,
    LogicalKeyboardKey.arrowLeft: TvRemoteKey.left,
    LogicalKeyboardKey.arrowRight: TvRemoteKey.right,
    LogicalKeyboardKey.select: TvRemoteKey.select,
    LogicalKeyboardKey.enter: TvRemoteKey.select,
    LogicalKeyboardKey.space: TvRemoteKey.select,
    LogicalKeyboardKey.escape: TvRemoteKey.back,
    LogicalKeyboardKey.backspace: TvRemoteKey.back,
    LogicalKeyboardKey.mediaPlay: TvRemoteKey.play,
    LogicalKeyboardKey.mediaPause: TvRemoteKey.pause,
    LogicalKeyboardKey.mediaPlayPause: TvRemoteKey.playPause,
    LogicalKeyboardKey.mediaStop: TvRemoteKey.stop,
    LogicalKeyboardKey.mediaFastForward: TvRemoteKey.fastForward,
    LogicalKeyboardKey.mediaRewind: TvRemoteKey.rewind,
    LogicalKeyboardKey.audioVolumeUp: TvRemoteKey.volumeUp,
    LogicalKeyboardKey.audioVolumeDown: TvRemoteKey.volumeDown,
    LogicalKeyboardKey.audioVolumeMute: TvRemoteKey.mute,
    LogicalKeyboardKey.home: TvRemoteKey.home,
    LogicalKeyboardKey.contextMenu: TvRemoteKey.menu,
  };
  
  // Handle TV remote key events
  static bool handleKeyEvent(KeyEvent event, BuildContext context) {
    if (event is KeyDownEvent) {
      final remoteKey = keyMappings[event.logicalKey];
      if (remoteKey != null) {
        return _handleRemoteKey(remoteKey, context);
      }
    }
    return false;
  }
  
  static bool _handleRemoteKey(TvRemoteKey key, BuildContext context) {
    switch (key) {
      case TvRemoteKey.up:
        return _moveFocus(context, TraversalDirection.up);
      case TvRemoteKey.down:
        return _moveFocus(context, TraversalDirection.down);
      case TvRemoteKey.left:
        return _moveFocus(context, TraversalDirection.left);
      case TvRemoteKey.right:
        return _moveFocus(context, TraversalDirection.right);
      case TvRemoteKey.select:
        return _activateFocusedWidget(context);
      case TvRemoteKey.back:
        return _handleBackKey(context);
      case TvRemoteKey.home:
        return _handleHomeKey(context);
      case TvRemoteKey.menu:
        return _handleMenuKey(context);
      default:
        return false;
    }
  }
  
  static bool _moveFocus(BuildContext context, TraversalDirection direction) {
    final focusScope = FocusScope.of(context);
    return focusScope.focusInDirection(direction);
  }
  
  static bool _activateFocusedWidget(BuildContext context) {
    final focusScope = FocusScope.of(context);
    final focusedWidget = focusScope.focusedChild;
    
    if (focusedWidget != null) {
      // Simulate tap on focused widget
      focusedWidget.context?.findRenderObject()?.let((renderObject) {
        if (renderObject is RenderBox) {
          final center = renderObject.size.center(Offset.zero);
          final globalPosition = renderObject.localToGlobal(center);
          
          // Create synthetic tap events
          final details = TapDownDetails(globalPosition: globalPosition);
          GestureBinding.instance.handlePointerEvent(
            PointerDownEvent(position: globalPosition),
          );
          GestureBinding.instance.handlePointerEvent(
            PointerUpEvent(position: globalPosition),
          );
        }
      });
      return true;
    }
    return false;
  }
  
  static bool _handleBackKey(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
      return true;
    }
    return false;
  }
  
  static bool _handleHomeKey(BuildContext context) {
    Navigator.of(context).popUntil((route) => route.isFirst);
    return true;
  }
  
  static bool _handleMenuKey(BuildContext context) {
    // Show context menu or options
    return false;
  }
}

enum TvRemoteKey {
  up,
  down,
  left,
  right,
  select,
  back,
  home,
  menu,
  play,
  pause,
  playPause,
  stop,
  fastForward,
  rewind,
  volumeUp,
  volumeDown,
  mute,
}

// TV Focusable Widget
class TvFocusableWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onFocus;
  final VoidCallback? onUnfocus;
  final bool autofocus;
  final FocusNode? focusNode;
  final double focusScale;
  final Duration animationDuration;
  final BorderRadius? borderRadius;
  final Color? focusColor;

  const TvFocusableWidget({
    super.key,
    required this.child,
    this.onTap,
    this.onFocus,
    this.onUnfocus,
    this.autofocus = false,
    this.focusNode,
    this.focusScale = TvNavigationHelper.focusScale,
    this.animationDuration = TvNavigationHelper.focusAnimationDuration,
    this.borderRadius,
    this.focusColor,
  });

  @override
  State<TvFocusableWidget> createState() => _TvFocusableWidgetState();
}

class _TvFocusableWidgetState extends State<TvFocusableWidget>
    with SingleTickerProviderStateMixin {
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
    
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.focusScale,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (_isFocused) {
      _animationController.forward();
      widget.onFocus?.call();
    } else {
      _animationController.reverse();
      widget.onUnfocus?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent && 
            (event.logicalKey == LogicalKeyboardKey.select ||
             event.logicalKey == LogicalKeyboardKey.enter ||
             event.logicalKey == LogicalKeyboardKey.space)) {
          widget.onTap?.call();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
                  border: _isFocused
                      ? Border.all(
                          color: widget.focusColor ?? Theme.of(context).primaryColor,
                          width: 3,
                        )
                      : null,
                  boxShadow: _isFocused
                      ? [
                          BoxShadow(
                            color: (widget.focusColor ?? Theme.of(context).primaryColor)
                                .withOpacity(0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ]
                      : null,
                ),
                child: widget.child,
              ),
            );
          },
        ),
      ),
    );
  }
}

// TV Grid Navigation
class TvGridView extends StatefulWidget {
  final List<Widget> children;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final EdgeInsets padding;
  final ScrollController? scrollController;

  const TvGridView({
    super.key,
    required this.children,
    required this.crossAxisCount,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.padding = EdgeInsets.zero,
    this.scrollController,
  });

  @override
  State<TvGridView> createState() => _TvGridViewState();
}

class _TvGridViewState extends State<TvGridView> {
  late ScrollController _scrollController;
  int _focusedIndex = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _scrollToFocusedItem() {
    final itemHeight = 200.0; // Approximate item height
    final rowIndex = _focusedIndex ~/ widget.crossAxisCount;
    final targetOffset = rowIndex * (itemHeight + widget.mainAxisSpacing);
    
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        mainAxisSpacing: widget.mainAxisSpacing,
        crossAxisSpacing: widget.crossAxisSpacing,
        childAspectRatio: 2 / 3,
      ),
      itemCount: widget.children.length,
      itemBuilder: (context, index) {
        return TvFocusableWidget(
          autofocus: index == 0,
          onFocus: () {
            _focusedIndex = index;
            _scrollToFocusedItem();
          },
          child: widget.children[index],
        );
      },
    );
  }
}

// Extension for nullable objects
extension NullableExtension<T> on T? {
  R? let<R>(R Function(T) block) {
    final value = this;
    return value != null ? block(value) : null;
  }
}
