name: streamflix_app
description: StreamFlix - Movies & TV Shows Streaming App
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.7
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # State Management
  provider: ^6.1.1
  get: ^4.6.6
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Media Player
  video_player: ^2.7.2
  chewie: ^1.7.1
  wakelock: ^0.6.2
  
  # TV Support
  flutter_tv: ^0.1.0
  
  # Utils
  intl: ^0.18.1
  url_launcher: ^6.2.1
  connectivity_plus: ^5.0.1
  device_info_plus: ^9.1.1
  
  # UI Enhancements
  flutter_staggered_grid_view: ^0.7.0
  smooth_page_indicator: ^1.1.0
  pull_to_refresh: ^2.0.0
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
