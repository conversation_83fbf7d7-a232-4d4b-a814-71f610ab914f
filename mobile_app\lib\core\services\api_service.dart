import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'storage_service.dart';

class ApiService {
  static late Dio _dio;
  static const String _baseUrl = AppConstants.apiUrl;
  
  static Future<void> init() async {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: AppConstants.connectionTimeout,
      receiveTimeout: AppConstants.receiveTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    // Add interceptors
    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(LoggingInterceptor());
    _dio.interceptors.add(RetryInterceptor());
  }
  
  // Auth endpoints
  static Future<ApiResponse> login(String username, String password) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'username': username,
        'password': password,
      });
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> register(Map<String, dynamic> userData) async {
    try {
      final response = await _dio.post('/auth/register', data: userData);
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> logout() async {
    try {
      final response = await _dio.post('/auth/logout');
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  // Content endpoints
  static Future<ApiResponse> getMovies({
    int page = 1,
    String? search,
    String? genre,
    String? filter,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        if (search != null) 'search': search,
        if (genre != null) 'genre': genre,
        if (filter != null) 'filter': filter,
      };
      
      final response = await _dio.get('/movies', queryParameters: queryParams);
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> getTvShows({
    int page = 1,
    String? search,
    String? genre,
    String? filter,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        if (search != null) 'search': search,
        if (genre != null) 'genre': genre,
        if (filter != null) 'filter': filter,
      };
      
      final response = await _dio.get('/tv-shows', queryParameters: queryParams);
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> getMovieDetails(int tmdbId) async {
    try {
      final response = await _dio.get('/movies/$tmdbId');
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> getTvShowDetails(int tmdbId) async {
    try {
      final response = await _dio.get('/tv-shows/$tmdbId');
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> getEmbedUrls(
    String contentType,
    int tmdbId, {
    int? season,
    int? episode,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'type': contentType,
        'tmdb_id': tmdbId,
        if (season != null) 'season': season,
        if (episode != null) 'episode': episode,
      };
      
      final response = await _dio.get('/embed-urls', queryParameters: queryParams);
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  // Watchlist endpoints
  static Future<ApiResponse> getWatchlist() async {
    try {
      final response = await _dio.get('/watchlist');
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> addToWatchlist(int contentId, String contentType) async {
    try {
      final response = await _dio.post('/watchlist', data: {
        'content_id': contentId,
        'content_type': contentType,
      });
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  static Future<ApiResponse> removeFromWatchlist(int contentId, String contentType) async {
    try {
      final response = await _dio.delete('/watchlist', data: {
        'content_id': contentId,
        'content_type': contentType,
      });
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  // Search endpoints
  static Future<ApiResponse> search(String query, {int page = 1}) async {
    try {
      final response = await _dio.get('/search', queryParameters: {
        'q': query,
        'page': page,
      });
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  // Genres endpoints
  static Future<ApiResponse> getGenres() async {
    try {
      final response = await _dio.get('/genres');
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
  
  // Stats endpoints
  static Future<ApiResponse> getStats() async {
    try {
      final response = await _dio.get('/stats');
      return ApiResponse.fromResponse(response);
    } catch (e) {
      return ApiResponse.error(e.toString());
    }
  }
}

// API Response Model
class ApiResponse {
  final bool success;
  final dynamic data;
  final String? message;
  final int? statusCode;
  
  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
  });
  
  factory ApiResponse.fromResponse(Response response) {
    return ApiResponse(
      success: response.statusCode == 200,
      data: response.data,
      statusCode: response.statusCode,
    );
  }
  
  factory ApiResponse.error(String message) {
    return ApiResponse(
      success: false,
      message: message,
    );
  }
}

// Auth Interceptor
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await StorageService.getToken();
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired, logout user
      await StorageService.clearToken();
      // Navigate to login screen
    }
    handler.next(err);
  }
}

// Logging Interceptor
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      debugPrint('REQUEST[${options.method}] => PATH: ${options.path}');
      debugPrint('Headers: ${options.headers}');
      debugPrint('Data: ${options.data}');
    }
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      debugPrint('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
      debugPrint('Data: ${response.data}');
    }
    handler.next(response);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      debugPrint('ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
      debugPrint('Message: ${err.message}');
    }
    handler.next(err);
  }
}

// Retry Interceptor
class RetryInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout) {
      
      final requestOptions = err.requestOptions;
      final retryCount = requestOptions.extra['retryCount'] ?? 0;
      
      if (retryCount < AppConstants.maxRetries) {
        requestOptions.extra['retryCount'] = retryCount + 1;
        
        // Wait before retry
        await Future.delayed(Duration(seconds: retryCount + 1));
        
        try {
          final response = await _dio.fetch(requestOptions);
          handler.resolve(response);
          return;
        } catch (e) {
          // Continue with original error if retry fails
        }
      }
    }
    
    handler.next(err);
  }
}
