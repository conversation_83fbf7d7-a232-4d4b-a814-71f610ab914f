import 'package:flutter/foundation.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';

class AuthProvider extends ChangeNotifier {
  bool _isLoading = false;
  bool _isLoggedIn = false;
  String? _errorMessage;
  Map<String, dynamic>? _user;

  // Getters
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _isLoggedIn;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic>? get user => _user;

  // Initialize auth state
  Future<void> initAuth() async {
    try {
      final hasToken = await StorageService.hasToken();
      if (hasToken) {
        final userData = await StorageService.getUserData();
        if (userData != null) {
          _user = userData;
          _isLoggedIn = true;
          notifyListeners();
        }
      }
    } catch (e) {
      debugPrint('Error initializing auth: $e');
    }
  }

  // Login
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.login(username, password);
      
      if (response.success && response.data != null) {
        final token = response.data['token'];
        final userData = response.data['user'];
        
        if (token != null && userData != null) {
          await StorageService.saveToken(token);
          await StorageService.saveUserData(userData);
          
          _user = userData;
          _isLoggedIn = true;
          _setLoading(false);
          
          return true;
        }
      }
      
      _setError(response.message ?? 'Login failed');
      _setLoading(false);
      return false;
      
    } catch (e) {
      _setError('Network error. Please try again.');
      _setLoading(false);
      return false;
    }
  }

  // Register
  Future<bool> register(Map<String, dynamic> userData) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.register(userData);
      
      if (response.success && response.data != null) {
        final token = response.data['token'];
        final user = response.data['user'];
        
        if (token != null && user != null) {
          await StorageService.saveToken(token);
          await StorageService.saveUserData(user);
          
          _user = user;
          _isLoggedIn = true;
          _setLoading(false);
          
          return true;
        }
      }
      
      _setError(response.message ?? 'Registration failed');
      _setLoading(false);
      return false;
      
    } catch (e) {
      _setError('Network error. Please try again.');
      _setLoading(false);
      return false;
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      await ApiService.logout();
    } catch (e) {
      debugPrint('Error during logout: $e');
    }

    await StorageService.clearToken();
    await StorageService.clearUserData();
    
    _user = null;
    _isLoggedIn = false;
    _setLoading(false);
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> profileData) async {
    _setLoading(true);
    _clearError();

    try {
      // API call to update profile
      // final response = await ApiService.updateProfile(profileData);
      
      // For now, just update local storage
      if (_user != null) {
        _user!.addAll(profileData);
        await StorageService.saveUserData(_user!);
        _setLoading(false);
        return true;
      }
      
      _setError('User not found');
      _setLoading(false);
      return false;
      
    } catch (e) {
      _setError('Failed to update profile');
      _setLoading(false);
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    _setLoading(true);
    _clearError();

    try {
      // API call to change password
      // final response = await ApiService.changePassword(currentPassword, newPassword);
      
      // For now, simulate success
      await Future.delayed(const Duration(seconds: 1));
      _setLoading(false);
      return true;
      
    } catch (e) {
      _setError('Failed to change password');
      _setLoading(false);
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      // API call to send reset email
      // final response = await ApiService.forgotPassword(email);
      
      // For now, simulate success
      await Future.delayed(const Duration(seconds: 1));
      _setLoading(false);
      return true;
      
    } catch (e) {
      _setError('Failed to send reset email');
      _setLoading(false);
      return false;
    }
  }

  // Verify email
  Future<bool> verifyEmail(String code) async {
    _setLoading(true);
    _clearError();

    try {
      // API call to verify email
      // final response = await ApiService.verifyEmail(code);
      
      // For now, simulate success
      await Future.delayed(const Duration(seconds: 1));
      
      if (_user != null) {
        _user!['email_verified'] = true;
        await StorageService.saveUserData(_user!);
      }
      
      _setLoading(false);
      return true;
      
    } catch (e) {
      _setError('Failed to verify email');
      _setLoading(false);
      return false;
    }
  }

  // Check if user is admin
  bool get isAdmin {
    return _user?['role'] == 'admin' || _user?['is_admin'] == true;
  }

  // Check if user is premium
  bool get isPremium {
    return _user?['is_premium'] == true || _user?['subscription_active'] == true;
  }

  // Get user avatar
  String? get userAvatar {
    return _user?['avatar'] ?? _user?['profile_picture'];
  }

  // Get user display name
  String get displayName {
    return _user?['display_name'] ?? 
           _user?['full_name'] ?? 
           _user?['username'] ?? 
           'User';
  }

  // Get user email
  String? get userEmail {
    return _user?['email'];
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear all auth data
  void clearAuth() {
    _user = null;
    _isLoggedIn = false;
    _errorMessage = null;
    notifyListeners();
  }
}
