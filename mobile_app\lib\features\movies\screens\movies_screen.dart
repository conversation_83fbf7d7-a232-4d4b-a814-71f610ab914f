import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/device_utils.dart';
import '../providers/movies_provider.dart';
import '../widgets/movie_card.dart';
import '../widgets/movie_filter_bar.dart';
import '../../player/screens/player_screen.dart';

class MoviesScreen extends StatefulWidget {
  const MoviesScreen({super.key});

  @override
  State<MoviesScreen> createState() => _MoviesScreenState();
}

class _MoviesScreenState extends State<MoviesScreen>
    with AutomaticKeepAliveClientMixin {
  late ScrollController _scrollController;
  final TextEditingController _searchController = TextEditingController();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MoviesProvider>().loadMovies();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      context.read<MoviesProvider>().loadMoreMovies();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppTheme.darkBg,
      appBar: _buildAppBar(),
      body: Consumer<MoviesProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Filter Bar
              MovieFilterBar(
                selectedGenre: provider.selectedGenre,
                selectedFilter: provider.selectedFilter,
                onGenreChanged: provider.setGenre,
                onFilterChanged: provider.setFilter,
              ),
              
              // Movies Grid
              Expanded(
                child: _buildMoviesGrid(provider),
              ),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Movies'),
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showSearchDialog,
        ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
        ),
      ],
    );
  }

  Widget _buildMoviesGrid(MoviesProvider provider) {
    if (provider.isLoading && provider.movies.isEmpty) {
      return _buildLoadingGrid();
    }

    if (provider.hasError && provider.movies.isEmpty) {
      return _buildErrorState(provider);
    }

    if (provider.movies.isEmpty) {
      return _buildEmptyState();
    }

    final crossAxisCount = DeviceUtils.getGridCrossAxisCount(context);
    
    return RefreshIndicator(
      onRefresh: () => provider.refreshMovies(),
      color: AppTheme.primaryColor,
      backgroundColor: AppTheme.cardBg,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverMasonryGrid.count(
              crossAxisCount: crossAxisCount,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childCount: provider.movies.length + (provider.isLoadingMore ? 2 : 0),
              itemBuilder: (context, index) {
                if (index >= provider.movies.length) {
                  return _buildLoadingCard();
                }
                
                final movie = provider.movies[index];
                return MovieCard(
                  movie: movie,
                  onTap: () => _navigateToPlayer(movie),
                  onWatchlistTap: () => _toggleWatchlist(movie, provider),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingGrid() {
    final crossAxisCount = DeviceUtils.getGridCrossAxisCount(context);
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: MasonryGridView.count(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        itemCount: 20,
        itemBuilder: (context, index) => _buildLoadingCard(),
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: AppTheme.cardBg,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildErrorState(MoviesProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              provider.errorMessage ?? 'Please try again later',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => provider.refreshMovies(),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.movie_outlined,
              size: 64,
              color: AppTheme.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'No movies found',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or search terms',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToPlayer(Map<String, dynamic> movie) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlayerScreen(
          tmdbId: movie['tmdb_id'],
          contentType: 'movie',
          title: movie['title'],
        ),
      ),
    );
  }

  void _toggleWatchlist(Map<String, dynamic> movie, MoviesProvider provider) {
    provider.toggleWatchlist(movie);
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardBg,
        title: const Text('Search Movies'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'Enter movie title...',
            prefixIcon: Icon(Icons.search),
          ),
          autofocus: true,
          onSubmitted: (query) {
            context.read<MoviesProvider>().searchMovies(query);
            Navigator.pop(context);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<MoviesProvider>().searchMovies(_searchController.text);
              Navigator.pop(context);
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    final provider = context.read<MoviesProvider>();
    
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.cardBg,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter Movies',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 24),
            
            // Genre Filter
            Text(
              'Genre',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ['All', 'Action', 'Comedy', 'Drama', 'Horror', 'Sci-Fi', 'Anime']
                  .map((genre) => FilterChip(
                        label: Text(genre),
                        selected: provider.selectedGenre == genre,
                        onSelected: (selected) {
                          provider.setGenre(selected ? genre : null);
                        },
                      ))
                  .toList(),
            ),
            
            const SizedBox(height: 24),
            
            // Sort Filter
            Text(
              'Sort By',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ['Popular', 'Latest', 'Rating', 'Trending']
                  .map((filter) => FilterChip(
                        label: Text(filter),
                        selected: provider.selectedFilter == filter.toLowerCase(),
                        onSelected: (selected) {
                          provider.setFilter(selected ? filter.toLowerCase() : null);
                        },
                      ))
                  .toList(),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      provider.clearFilters();
                      Navigator.pop(context);
                    },
                    child: const Text('Clear All'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Apply'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
