<?php
require_once 'includes/functions.php';

// Get parameters
$tmdb_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$type = isset($_GET['type']) ? sanitizeInput($_GET['type']) : 'movie';
$season = isset($_GET['season']) ? (int)$_GET['season'] : 1;
$episode = isset($_GET['episode']) ? (int)$_GET['episode'] : 1;

if (!$tmdb_id) {
    header('Location: index.php');
    exit();
}

$streamflix = new StreamFlix();

// Get content details
$content = null;
try {
    $db = new Database();
    $conn = $db->connect();

    if ($type === 'movie') {
        $stmt = $conn->prepare("SELECT * FROM movies WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        $stmt = $conn->prepare("SELECT * FROM tv_shows WHERE tmdb_id = :tmdb_id");
        $stmt->execute([':tmdb_id' => $tmdb_id]);
        $content = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get episode details for TV shows
        if ($content) {
            $stmt = $conn->prepare("
                SELECT e.*, s.season_number
                FROM episodes e
                JOIN seasons s ON e.season_id = s.id
                WHERE s.tv_show_id = :tv_show_id AND s.season_number = :season AND e.episode_number = :episode
            ");
            $stmt->execute([
                ':tv_show_id' => $content['id'],
                ':season' => $season,
                ':episode' => $episode
            ]);
            $episode_data = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
} catch (Exception $e) {
    error_log("Player error: " . $e->getMessage());
    header('Location: index.php');
    exit();
}

if (!$content) {
    header('Location: index.php');
    exit();
}

// Generate embed URLs
$embed_urls = $streamflix->getEmbedUrls($type, $tmdb_id, $season, $episode);

$title = $content['title'] ?? $content['name'];
if ($type === 'tv_show') {
    $title .= " - S{$season}E{$episode}";
    if (isset($episode_data) && $episode_data['name']) {
        $title .= ": " . $episode_data['name'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($title); ?> - <?php echo SITE_NAME; ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Ad Blocking CSS -->
    <style>
        /* Block common ad elements */
        .ads, .ad, .advertisement, .google-ads, .adsystem, .adsbygoogle,
        [class*="ad-"], [id*="ad-"], [class*="ads-"], [id*="ads-"],
        iframe[src*="googlesyndication"], iframe[src*="doubleclick"],
        iframe[src*="googletagmanager"], iframe[src*="facebook.com/tr"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
        }
        
        /* Player specific styles */
        .player-container {
            background: #000;
            position: relative;
            margin: 80px auto 20px;
            max-width: 1200px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
        }
        
        .player-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
        }
        
        .player-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            background: #000;
        }
        
        .server-selector {
            background: var(--secondary-color);
            padding: 20px;
            border-top: 1px solid var(--border-color);
        }
        
        .server-label {
            color: var(--text-primary);
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .server-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .server-btn {
            padding: 10px 20px;
            background: var(--border-color);
            color: var(--text-primary);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .server-btn:hover {
            background: var(--primary-color);
        }
        
        .server-btn.active {
            background: var(--primary-color);
            box-shadow: 0 2px 8px rgba(229, 9, 20, 0.3);
        }
        
        .content-info {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--secondary-color);
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .content-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--text-primary);
        }
        
        .content-meta {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            color: var(--text-secondary);
        }
        
        .content-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--text-primary);
            text-decoration: none;
            padding: 10px 20px;
            background: var(--border-color);
            border-radius: 4px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background: var(--primary-color);
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }
        
        .error-message h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        /* Episode Selector Styles */
        .episode-selector {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
        }

        .season-episode-controls {
            background: var(--secondary-color);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            gap: 30px;
            align-items: end;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group label {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .control-group select {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 1rem;
            min-width: 150px;
        }

        .control-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .episodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }

        .episode-card {
            background: var(--secondary-color);
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .episode-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .episode-card.active {
            border-color: var(--primary-color);
            background: rgba(229, 9, 20, 0.1);
        }

        .episode-thumbnail {
            width: 100%;
            height: 160px;
            object-fit: cover;
            background: var(--dark-bg);
        }

        .episode-info {
            padding: 15px;
        }

        .episode-number {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .episode-title {
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .episode-overview {
            color: var(--text-secondary);
            font-size: 0.85rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .episode-runtime {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-top: 8px;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .player-container {
                margin: 70px 10px 20px;
            }

            .content-info {
                margin: 0 10px 20px;
                padding: 15px;
            }

            .content-title {
                font-size: 1.5rem;
            }

            .server-buttons {
                justify-content: center;
            }

            .server-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }

            .season-episode-controls {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .control-group {
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
            }

            .control-group select {
                min-width: 120px;
            }

            .episodes-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .episode-thumbnail {
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header scrolled">
        <nav class="navbar">
            <a href="index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <a href="javascript:history.back()" class="back-btn">← Back</a>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Content Info -->
        <div class="content-info">
            <h1 class="content-title"><?php echo htmlspecialchars($title); ?></h1>
            <div class="content-meta">
                <span><?php echo date('Y', strtotime($content['release_date'] ?? $content['first_air_date'])); ?></span>
                <span>★ <?php echo $content['vote_average']; ?>/10</span>
                <?php if ($content['runtime']): ?>
                    <span><?php echo formatRuntime($content['runtime']); ?></span>
                <?php endif; ?>
            </div>
            <?php if ($content['overview']): ?>
                <p class="content-description"><?php echo htmlspecialchars($content['overview']); ?></p>
            <?php endif; ?>
        </div>

        <!-- Player -->
        <div class="player-container">
            <div class="player-wrapper">
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-spinner"></div>
                </div>
                
                <iframe 
                    id="playerIframe" 
                    class="player-iframe" 
                    src="<?php echo $embed_urls[0]['url']; ?>"
                    allowfullscreen
                    webkitallowfullscreen
                    mozallowfullscreen
                    allow="autoplay; fullscreen; picture-in-picture"
                    sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-presentation"
                    referrerpolicy="no-referrer">
                </iframe>
            </div>
            
            <!-- Server Selector -->
            <div class="server-selector">
                <div class="server-label">Select Server:</div>
                <div class="server-buttons">
                    <?php foreach ($embed_urls as $index => $server): ?>
                        <button 
                            class="server-btn <?php echo $index === 0 ? 'active' : ''; ?>" 
                            data-url="<?php echo htmlspecialchars($server['url']); ?>"
                            data-server="<?php echo htmlspecialchars($server['name']); ?>">
                            <?php echo htmlspecialchars($server['name']); ?>
                        </button>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Episode Selector for TV Shows -->
        <?php if ($type === 'tv_show'): ?>
        <div class="episode-selector">
            <div class="season-episode-controls">
                <div class="control-group">
                    <label for="seasonSelect">Season:</label>
                    <select id="seasonSelect" onchange="loadEpisodes()">
                        <!-- Seasons will be loaded here -->
                    </select>
                </div>
                <div class="control-group">
                    <label for="episodeSelect">Episode:</label>
                    <select id="episodeSelect" onchange="changeEpisode()">
                        <!-- Episodes will be loaded here -->
                    </select>
                </div>
            </div>

            <div class="episodes-grid" id="episodesGrid">
                <!-- Episode cards will be loaded here -->
            </div>
        </div>
        <?php endif; ?>
    </main>

    <!-- Ad Blocking JavaScript -->
    <script>
        // Advanced Ad Blocking
        (function() {
            'use strict';
            
            // Block ad domains
            const adDomains = [
                'googlesyndication.com',
                'doubleclick.net',
                'googletagmanager.com',
                'google-analytics.com',
                'facebook.com/tr',
                'analytics.google.com',
                'ads.yahoo.com',
                'adsystem.amazon.com'
            ];
            
            // Override fetch to block ad requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string') {
                    for (const domain of adDomains) {
                        if (url.includes(domain)) {
                            return Promise.reject(new Error('Blocked'));
                        }
                    }
                }
                return originalFetch.apply(this, args);
            };
            
            // Block ad scripts
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) {
                            // Block script tags with ad domains
                            if (node.tagName === 'SCRIPT' && node.src) {
                                for (const domain of adDomains) {
                                    if (node.src.includes(domain)) {
                                        node.remove();
                                        return;
                                    }
                                }
                            }
                            
                            // Block iframe ads
                            if (node.tagName === 'IFRAME' && node.src) {
                                for (const domain of adDomains) {
                                    if (node.src.includes(domain)) {
                                        node.remove();
                                        return;
                                    }
                                }
                            }
                            
                            // Remove elements with ad-related classes/ids
                            const adSelectors = [
                                '[class*="ad"]', '[id*="ad"]', '[class*="ads"]', '[id*="ads"]',
                                '.advertisement', '.google-ads', '.adsense'
                            ];
                            
                            adSelectors.forEach(selector => {
                                try {
                                    const adElements = node.querySelectorAll ? node.querySelectorAll(selector) : [];
                                    adElements.forEach(el => el.remove());
                                } catch (e) {}
                            });
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        })();
        
        // Player functionality
        document.addEventListener('DOMContentLoaded', function() {
            const playerIframe = document.getElementById('playerIframe');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const serverButtons = document.querySelectorAll('.server-btn');
            
            // Hide loading overlay when iframe loads
            playerIframe.addEventListener('load', function() {
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 1000);
            });
            
            // Server switching
            serverButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const url = this.dataset.url;
                    const serverName = this.dataset.server;
                    
                    // Show loading
                    loadingOverlay.style.display = 'flex';
                    
                    // Update active button
                    serverButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Change iframe source
                    playerIframe.src = url;
                    
                    console.log('Switched to server:', serverName);
                });
            });
            
            // Error handling
            playerIframe.addEventListener('error', function() {
                loadingOverlay.innerHTML = `
                    <div class="error-message">
                        <h3>Player Error</h3>
                        <p>Unable to load this server. Please try another server.</p>
                        <button class="btn btn-primary" onclick="location.reload()">Retry</button>
                    </div>
                `;
            });
            
            // Prevent right-click on player
            playerIframe.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });
        });
        
        <?php if ($type === 'tv_show'): ?>
        // TV Show specific variables
        const tmdbId = <?php echo $tmdb_id; ?>;
        let currentSeason = <?php echo $season; ?>;
        let currentEpisode = <?php echo $episode; ?>;

        // Load seasons for TV shows
        async function loadSeasons() {
            try {
                const response = await fetch(`api/tv-seasons.php?tmdb_id=${tmdbId}`);
                const data = await response.json();

                if (data.success && data.seasons.length > 0) {
                    const seasonSelect = document.getElementById('seasonSelect');
                    seasonSelect.innerHTML = '';

                    data.seasons.forEach(season => {
                        const option = document.createElement('option');
                        option.value = season.season_number;
                        option.textContent = `Season ${season.season_number}`;
                        option.selected = season.season_number == currentSeason;
                        seasonSelect.appendChild(option);
                    });

                    loadEpisodes();
                } else {
                    // Fallback: create seasons 1-10
                    const seasonSelect = document.getElementById('seasonSelect');
                    seasonSelect.innerHTML = '';

                    for (let i = 1; i <= 10; i++) {
                        const option = document.createElement('option');
                        option.value = i;
                        option.textContent = `Season ${i}`;
                        option.selected = i == currentSeason;
                        seasonSelect.appendChild(option);
                    }

                    loadEpisodes();
                }
            } catch (error) {
                console.error('Error loading seasons:', error);
                // Fallback: create seasons 1-10
                const seasonSelect = document.getElementById('seasonSelect');
                seasonSelect.innerHTML = '';

                for (let i = 1; i <= 10; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = `Season ${i}`;
                    option.selected = i == currentSeason;
                    seasonSelect.appendChild(option);
                }

                loadEpisodes();
            }
        }

        async function loadEpisodes() {
            const seasonNumber = document.getElementById('seasonSelect').value;
            currentSeason = seasonNumber;

            try {
                const response = await fetch(`api/tv-episodes.php?tmdb_id=${tmdbId}&season=${seasonNumber}`);
                const data = await response.json();

                if (data.success) {
                    // Update episode select
                    const episodeSelect = document.getElementById('episodeSelect');
                    episodeSelect.innerHTML = '';

                    data.episodes.forEach(episode => {
                        const option = document.createElement('option');
                        option.value = episode.episode_number;
                        option.textContent = `Episode ${episode.episode_number}`;
                        option.selected = episode.episode_number == currentEpisode;
                        episodeSelect.appendChild(option);
                    });

                    // Update episodes grid
                    displayEpisodes(data.episodes);
                } else {
                    // Fallback: create episodes 1-24
                    const episodeSelect = document.getElementById('episodeSelect');
                    episodeSelect.innerHTML = '';

                    for (let i = 1; i <= 24; i++) {
                        const option = document.createElement('option');
                        option.value = i;
                        option.textContent = `Episode ${i}`;
                        option.selected = i == currentEpisode;
                        episodeSelect.appendChild(option);
                    }

                    // Create fallback episode cards
                    const episodesGrid = document.getElementById('episodesGrid');
                    episodesGrid.innerHTML = '';

                    for (let i = 1; i <= 24; i++) {
                        const episodeCard = document.createElement('div');
                        episodeCard.className = 'episode-card';
                        if (i == currentEpisode) {
                            episodeCard.classList.add('active');
                        }

                        episodeCard.innerHTML = `
                            <img src="assets/images/no-image.svg" alt="Episode ${i}" class="episode-thumbnail">
                            <div class="episode-info">
                                <div class="episode-number">Episode ${i}</div>
                                <div class="episode-title">Episode ${i}</div>
                                <div class="episode-overview">Click to watch this episode.</div>
                            </div>
                        `;

                        episodeCard.addEventListener('click', () => {
                            changeToEpisode(seasonNumber, i);
                        });

                        episodesGrid.appendChild(episodeCard);
                    }
                }
            } catch (error) {
                console.error('Error loading episodes:', error);
            }
        }

        function displayEpisodes(episodes) {
            const episodesGrid = document.getElementById('episodesGrid');
            episodesGrid.innerHTML = '';

            episodes.forEach(episode => {
                const episodeCard = document.createElement('div');
                episodeCard.className = 'episode-card';
                if (episode.episode_number == currentEpisode) {
                    episodeCard.classList.add('active');
                }

                episodeCard.innerHTML = `
                    <img src="${episode.still_path ? 'https://image.tmdb.org/t/p/w500' + episode.still_path : 'assets/images/no-image.svg'}"
                         alt="Episode ${episode.episode_number}" class="episode-thumbnail">
                    <div class="episode-info">
                        <div class="episode-number">Episode ${episode.episode_number}</div>
                        <div class="episode-title">${episode.name || 'Episode ' + episode.episode_number}</div>
                        <div class="episode-overview">${episode.overview || 'No description available.'}</div>
                        ${episode.runtime ? `<div class="episode-runtime">${episode.runtime} min</div>` : ''}
                    </div>
                `;

                episodeCard.addEventListener('click', () => {
                    changeToEpisode(currentSeason, episode.episode_number);
                });

                episodesGrid.appendChild(episodeCard);
            });
        }

        function changeEpisode() {
            const episodeNumber = document.getElementById('episodeSelect').value;
            changeToEpisode(currentSeason, episodeNumber);
        }

        function changeToEpisode(season, episode) {
            const newUrl = `player.php?id=${tmdbId}&type=tv_show&season=${season}&episode=${episode}`;
            window.location.href = newUrl;
        }

        // Load seasons and episodes on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadSeasons();
        });
        <?php endif; ?>
    </script>
</body>
</html>
