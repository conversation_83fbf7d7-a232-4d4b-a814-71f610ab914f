<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

// Handle server actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_server') {
        $name = sanitizeInput($_POST['name'] ?? '');
        $movie_url = sanitizeInput($_POST['movie_url'] ?? '');
        $tv_url = sanitizeInput($_POST['tv_url'] ?? '');
        $priority = (int)($_POST['priority'] ?? 1);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        if (!empty($name) && !empty($movie_url) && !empty($tv_url)) {
            try {
                $db = new Database();
                $conn = $db->connect();
                
                $stmt = $conn->prepare("
                    INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active, created_at)
                    VALUES (:name, :movie_url, :tv_url, :priority, :is_active, NOW())
                ");
                
                $stmt->execute([
                    ':name' => $name,
                    ':movie_url' => $movie_url,
                    ':tv_url' => $tv_url,
                    ':priority' => $priority,
                    ':is_active' => $is_active
                ]);
                
                $message = 'Server added successfully!';
            } catch (Exception $e) {
                $error = 'Failed to add server: ' . $e->getMessage();
            }
        } else {
            $error = 'Please fill in all required fields.';
        }
    }
    
    if ($action === 'update_server') {
        $server_id = (int)($_POST['server_id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $movie_url = sanitizeInput($_POST['movie_url'] ?? '');
        $tv_url = sanitizeInput($_POST['tv_url'] ?? '');
        $priority = (int)($_POST['priority'] ?? 1);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        if ($server_id > 0 && !empty($name) && !empty($movie_url) && !empty($tv_url)) {
            try {
                $db = new Database();
                $conn = $db->connect();
                
                $stmt = $conn->prepare("
                    UPDATE embed_servers 
                    SET name = :name, movie_url = :movie_url, tv_url = :tv_url, 
                        priority = :priority, is_active = :is_active
                    WHERE id = :id
                ");
                
                $stmt->execute([
                    ':name' => $name,
                    ':movie_url' => $movie_url,
                    ':tv_url' => $tv_url,
                    ':priority' => $priority,
                    ':is_active' => $is_active,
                    ':id' => $server_id
                ]);
                
                $message = 'Server updated successfully!';
            } catch (Exception $e) {
                $error = 'Failed to update server: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_server') {
        $server_id = (int)($_POST['server_id'] ?? 0);

        if ($server_id > 0) {
            try {
                $db = new Database();
                $conn = $db->connect();

                // Get server details before deletion
                $stmt = $conn->prepare("SELECT name FROM embed_servers WHERE id = :id");
                $stmt->execute([':id' => $server_id]);
                $server = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($server) {
                    // Begin transaction
                    $conn->beginTransaction();

                    // Delete the server
                    $stmt = $conn->prepare("DELETE FROM embed_servers WHERE id = :id");
                    $stmt->execute([':id' => $server_id]);

                    // Note: Server URLs are generated dynamically, so no need to clean up content
                    // The getEmbedUrls function will automatically exclude deleted servers

                    $conn->commit();
                    $message = "Server '{$server['name']}' deleted successfully! All content will automatically use remaining active servers.";
                } else {
                    $error = 'Server not found.';
                }
            } catch (Exception $e) {
                if (isset($conn)) {
                    $conn->rollBack();
                }
                $error = 'Failed to delete server: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'toggle_status') {
        $server_id = (int)($_POST['server_id'] ?? 0);
        
        if ($server_id > 0) {
            try {
                $db = new Database();
                $conn = $db->connect();
                
                $stmt = $conn->prepare("UPDATE embed_servers SET is_active = NOT is_active WHERE id = :id");
                $stmt->execute([':id' => $server_id]);
                
                $message = 'Server status updated!';
            } catch (Exception $e) {
                $error = 'Failed to update server status: ' . $e->getMessage();
            }
        }
    }
}

// Get all servers
try {
    $db = new Database();
    $conn = $db->connect();
    
    // Create table if it doesn't exist
    $conn->exec("
        CREATE TABLE IF NOT EXISTS embed_servers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            movie_url TEXT NOT NULL,
            tv_url TEXT NOT NULL,
            priority INT DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // Insert default servers if table is empty
    $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $default_servers = [
            ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
            ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
            ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
            ['SuperEmbed', 'https://multiembed.mov/directstream.php?video_id={id}', 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}', 4],
            ['EmbedSu', 'https://embed.su/embed/movie/{id}', 'https://embed.su/embed/tv/{id}/{season}/{episode}', 5],
            ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 6]
        ];
        
        foreach ($default_servers as $server) {
            $stmt = $conn->prepare("
                INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active, created_at)
                VALUES (?, ?, ?, ?, 1, NOW())
            ");
            $stmt->execute($server);
        }
    }
    
    $stmt = $conn->query("SELECT * FROM embed_servers ORDER BY priority ASC");
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $servers = [];
    $error = 'Database error: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Management - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .server-form {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 1rem;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .servers-table {
            background: var(--secondary-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .table th {
            background: var(--dark-bg);
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .table td {
            color: var(--text-secondary);
        }
        
        .server-name {
            color: var(--text-primary);
            font-weight: 500;
        }
        
        .server-url {
            font-family: monospace;
            font-size: 0.9rem;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-active {
            background: #28a745;
            color: white;
        }
        
        .badge-inactive {
            background: #dc3545;
            color: white;
        }
        
        .priority-badge {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 2px;
            transition: all 0.3s ease;
        }
        
        .btn-edit {
            background: #007bff;
            color: white;
        }
        
        .btn-edit:hover {
            background: #0056b3;
        }
        
        .btn-toggle {
            background: var(--border-color);
            color: var(--text-primary);
        }
        
        .btn-toggle:hover {
            background: var(--primary-color);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        .btn-test {
            background: #28a745;
            color: white;
        }
        
        .btn-test:hover {
            background: #1e7e34;
        }
        
        .help-text {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 5px;
        }
        
        .url-variables {
            background: var(--dark-bg);
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .url-variables h4 {
            color: var(--text-primary);
            margin-bottom: 10px;
        }
        
        .url-variables code {
            background: var(--border-color);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .server-url {
                max-width: 150px;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Server Management</h1>
            <p>Manage embed servers for streaming content</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php" class="active">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php">Maintenance</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($message)): ?>
            <div class="success-message"><?php echo $message; ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="error-message"><?php echo $error; ?></div>
        <?php endif; ?>

        <!-- Add/Edit Server Form -->
        <div class="server-form">
            <h2>Add New Server</h2>
            
            <form method="POST" id="serverForm">
                <input type="hidden" name="action" value="add_server" id="formAction">
                <input type="hidden" name="server_id" value="" id="serverId">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Server Name</label>
                        <input type="text" id="name" name="name" required>
                        <div class="help-text">Display name for the server</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" name="priority">
                            <option value="1">1 (Highest)</option>
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5 (Lowest)</option>
                        </select>
                        <div class="help-text">Lower numbers = higher priority</div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="movie_url">Movie URL Template</label>
                    <input type="text" id="movie_url" name="movie_url" required>
                    <div class="help-text">Use <code>{id}</code> as placeholder for TMDB ID</div>
                </div>
                
                <div class="form-group">
                    <label for="tv_url">TV Show URL Template</label>
                    <input type="text" id="tv_url" name="tv_url" required>
                    <div class="help-text">Use <code>{id}</code>, <code>{season}</code>, <code>{episode}</code> as placeholders</div>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="is_active" name="is_active" checked>
                    <label for="is_active">Active</label>
                </div>
                
                <div style="margin-top: 20px;">
                    <button type="submit" class="btn btn-primary" id="submitBtn">Add Server</button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()" id="cancelBtn" style="display: none;">Cancel</button>
                </div>
            </form>
            
            <div class="url-variables">
                <h4>Available Variables:</h4>
                <p><strong>Movies:</strong> <code>{id}</code> = TMDB Movie ID</p>
                <p><strong>TV Shows:</strong> <code>{id}</code> = TMDB TV ID, <code>{season}</code> = Season Number, <code>{episode}</code> = Episode Number</p>
                <p><strong>Example:</strong> https://example.com/embed/tv/{id}/{season}/{episode}</p>
            </div>
        </div>

        <!-- Servers Table -->
        <div class="servers-table">
            <table class="table">
                <thead>
                    <tr>
                        <th>Priority</th>
                        <th>Server Name</th>
                        <th>Movie URL</th>
                        <th>TV URL</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($servers as $server): ?>
                    <tr>
                        <td>
                            <span class="priority-badge"><?php echo $server['priority']; ?></span>
                        </td>
                        <td>
                            <div class="server-name"><?php echo htmlspecialchars($server['name']); ?></div>
                            <small>ID: <?php echo $server['id']; ?></small>
                        </td>
                        <td>
                            <div class="server-url" title="<?php echo htmlspecialchars($server['movie_url']); ?>">
                                <?php echo htmlspecialchars($server['movie_url']); ?>
                            </div>
                        </td>
                        <td>
                            <div class="server-url" title="<?php echo htmlspecialchars($server['tv_url']); ?>">
                                <?php echo htmlspecialchars($server['tv_url']); ?>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge <?php echo $server['is_active'] ? 'badge-active' : 'badge-inactive'; ?>">
                                <?php echo $server['is_active'] ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td>
                            <button class="action-btn btn-edit" onclick="editServer(<?php echo htmlspecialchars(json_encode($server)); ?>)">
                                Edit
                            </button>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="toggle_status">
                                <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                <button type="submit" class="action-btn btn-toggle">
                                    <?php echo $server['is_active'] ? 'Disable' : 'Enable'; ?>
                                </button>
                            </form>
                            
                            <button class="action-btn btn-test" onclick="testServer(<?php echo $server['id']; ?>)">
                                Test
                            </button>
                            
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this server?')">
                                <input type="hidden" name="action" value="delete_server">
                                <input type="hidden" name="server_id" value="<?php echo $server['id']; ?>">
                                <button type="submit" class="action-btn btn-delete">Delete</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function editServer(server) {
            document.getElementById('formAction').value = 'update_server';
            document.getElementById('serverId').value = server.id;
            document.getElementById('name').value = server.name;
            document.getElementById('movie_url').value = server.movie_url;
            document.getElementById('tv_url').value = server.tv_url;
            document.getElementById('priority').value = server.priority;
            document.getElementById('is_active').checked = server.is_active == 1;
            
            document.getElementById('submitBtn').textContent = 'Update Server';
            document.getElementById('cancelBtn').style.display = 'inline-block';
            
            document.querySelector('.server-form h2').textContent = 'Edit Server';
            document.getElementById('serverForm').scrollIntoView({ behavior: 'smooth' });
        }
        
        function resetForm() {
            document.getElementById('serverForm').reset();
            document.getElementById('formAction').value = 'add_server';
            document.getElementById('serverId').value = '';
            document.getElementById('submitBtn').textContent = 'Add Server';
            document.getElementById('cancelBtn').style.display = 'none';
            document.querySelector('.server-form h2').textContent = 'Add New Server';
        }
        
        async function testServer(serverId) {
            try {
                const response = await fetch('../api/test-server.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        server_id: serverId,
                        test_id: 550 // Test with Blade Runner movie
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Server test successful! Response time: ' + data.response_time + 'ms');
                } else {
                    alert('Server test failed: ' + data.message);
                }
            } catch (error) {
                alert('Server test failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
