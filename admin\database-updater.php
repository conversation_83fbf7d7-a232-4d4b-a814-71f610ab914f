<?php
// Check if config file exists, if not redirect to installer
if (!file_exists(__DIR__ . '/../config/database.php')) {
    header('Location: ../install.php');
    exit('Please run the installer first.');
}

require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    redirectTo('../login.php');
}

$updates_applied = [];
$errors = [];

// Handle database update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_database') {
    try {
        $db = new Database();
        $conn = $db->connect();
        
        // Start transaction
        $conn->beginTransaction();
        
        // Update 1: Create embed_servers table if not exists
        try {
            $conn->exec("
                CREATE TABLE IF NOT EXISTS embed_servers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    movie_url TEXT NOT NULL,
                    tv_url TEXT NOT NULL,
                    priority INT DEFAULT 1,
                    is_active BOOLEAN DEFAULT 1,
                    last_tested TIMESTAMP NULL,
                    last_response_time INT NULL,
                    last_status_code INT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_priority (priority),
                    INDEX idx_active (is_active)
                )
            ");
            $updates_applied[] = "Created/Updated embed_servers table";
        } catch (Exception $e) {
            $errors[] = "embed_servers table: " . $e->getMessage();
        }
        
        // Update 2: Add missing columns to existing tables
        try {
            // Add created_at to movies if not exists
            $conn->exec("ALTER TABLE movies ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            
            // Add created_at to tv_shows if not exists
            $conn->exec("ALTER TABLE tv_shows ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            
            // Add is_featured and is_trending to movies
            $conn->exec("ALTER TABLE movies ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT 0");
            $conn->exec("ALTER TABLE movies ADD COLUMN IF NOT EXISTS is_trending BOOLEAN DEFAULT 0");
            
            // Add is_featured and is_trending to tv_shows
            $conn->exec("ALTER TABLE tv_shows ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT 0");
            $conn->exec("ALTER TABLE tv_shows ADD COLUMN IF NOT EXISTS is_trending BOOLEAN DEFAULT 0");
            
            $updates_applied[] = "Added missing columns to movies and tv_shows tables";
        } catch (Exception $e) {
            $errors[] = "Adding columns: " . $e->getMessage();
        }
        
        // Update 3: Create site_settings table if not exists
        try {
            $conn->exec("
                CREATE TABLE IF NOT EXISTS site_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) NOT NULL UNIQUE,
                    setting_value TEXT,
                    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_key (setting_key)
                )
            ");
            $updates_applied[] = "Created/Updated site_settings table";
        } catch (Exception $e) {
            $errors[] = "site_settings table: " . $e->getMessage();
        }
        
        // Update 4: Create user_activity table for analytics
        try {
            $conn->exec("
                CREATE TABLE IF NOT EXISTS user_activity (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    activity_type ENUM('login', 'logout', 'view_movie', 'view_tv_show', 'search') NOT NULL,
                    content_type ENUM('movie', 'tv_show') NULL,
                    content_id INT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX idx_user_activity (user_id, created_at),
                    INDEX idx_activity_type (activity_type),
                    INDEX idx_created_at (created_at)
                )
            ");
            $updates_applied[] = "Created user_activity table for analytics";
        } catch (Exception $e) {
            $errors[] = "user_activity table: " . $e->getMessage();
        }
        
        // Update 5: Create watchlist table if not exists
        try {
            $conn->exec("
                CREATE TABLE IF NOT EXISTS watchlist (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    content_type ENUM('movie', 'tv_show') NOT NULL,
                    content_id INT NOT NULL,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_watchlist (user_id, content_type, content_id),
                    INDEX idx_user_watchlist (user_id)
                )
            ");
            $updates_applied[] = "Created watchlist table";
        } catch (Exception $e) {
            $errors[] = "watchlist table: " . $e->getMessage();
        }
        
        // Update 6: Insert default embed servers if table is empty
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
            $count = $stmt->fetchColumn();
            
            if ($count == 0) {
                $default_servers = [
                    ['AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1],
                    ['VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2],
                    ['VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3],
                    ['SuperEmbed', 'https://multiembed.mov/directstream.php?video_id={id}', 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}', 4],
                    ['EmbedSu', 'https://embed.su/embed/movie/{id}', 'https://embed.su/embed/tv/{id}/{season}/{episode}', 5],
                    ['LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 6]
                ];
                
                foreach ($default_servers as $server) {
                    $stmt = $conn->prepare("
                        INSERT INTO embed_servers (name, movie_url, tv_url, priority, is_active, created_at)
                        VALUES (?, ?, ?, ?, 1, NOW())
                    ");
                    $stmt->execute($server);
                }
                $updates_applied[] = "Inserted default embed servers (6 servers)";
            }
        } catch (Exception $e) {
            $errors[] = "Default servers: " . $e->getMessage();
        }
        
        // Update 7: Insert default site settings if table is empty
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM site_settings");
            $count = $stmt->fetchColumn();
            
            if ($count == 0) {
                $default_settings = [
                    ['site_name', 'StreamFlix', 'string', 'Website name'],
                    ['site_description', 'Premium Movie & TV Show Streaming Platform', 'string', 'Website description'],
                    ['tmdb_api_key', '', 'string', 'TMDB API Key for content import'],
                    ['maintenance_mode', '0', 'boolean', 'Enable maintenance mode'],
                    ['registration_enabled', '1', 'boolean', 'Allow new user registrations'],
                    ['featured_content_limit', '10', 'number', 'Number of featured content items'],
                    ['trending_content_limit', '20', 'number', 'Number of trending content items'],
                    ['max_servers_per_content', '6', 'number', 'Maximum embed servers per content'],
                    ['cache_duration', '3600', 'number', 'Cache duration in seconds'],
                    ['auto_import_trending', '0', 'boolean', 'Auto import trending content daily']
                ];
                
                foreach ($default_settings as $setting) {
                    $stmt = $conn->prepare("
                        INSERT INTO site_settings (setting_key, setting_value, setting_type, description)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute($setting);
                }
                $updates_applied[] = "Inserted default site settings (10 settings)";
            }
        } catch (Exception $e) {
            $errors[] = "Default settings: " . $e->getMessage();
        }
        
        // Update 8: Add indexes for better performance
        try {
            // Movies indexes
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_movies_tmdb ON movies(tmdb_id)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_movies_featured ON movies(is_featured)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_movies_trending ON movies(is_trending)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_movies_created ON movies(created_at)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_movies_popularity ON movies(popularity)");
            
            // TV Shows indexes
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tv_tmdb ON tv_shows(tmdb_id)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tv_featured ON tv_shows(is_featured)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tv_trending ON tv_shows(is_trending)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tv_created ON tv_shows(created_at)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tv_popularity ON tv_shows(popularity)");
            
            // Users indexes
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_users_created ON users(created_at)");
            
            $updates_applied[] = "Added performance indexes to tables";
        } catch (Exception $e) {
            $errors[] = "Adding indexes: " . $e->getMessage();
        }
        
        // Update 9: Create backup directory
        try {
            $backup_dir = __DIR__ . '/../backups';
            if (!is_dir($backup_dir)) {
                mkdir($backup_dir, 0755, true);
                file_put_contents($backup_dir . '/.htaccess', "Deny from all\n");
                $updates_applied[] = "Created backup directory with security";
            }
        } catch (Exception $e) {
            $errors[] = "Backup directory: " . $e->getMessage();
        }
        
        // Update 10: Create cache directories
        try {
            $cache_dirs = [
                __DIR__ . '/../cache',
                __DIR__ . '/../tmp',
                __DIR__ . '/../assets/cache'
            ];
            
            foreach ($cache_dirs as $dir) {
                if (!is_dir($dir)) {
                    mkdir($dir, 0755, true);
                    file_put_contents($dir . '/.htaccess', "Deny from all\n");
                }
            }
            $updates_applied[] = "Created cache directories with security";
        } catch (Exception $e) {
            $errors[] = "Cache directories: " . $e->getMessage();
        }
        
        // Commit transaction
        $conn->commit();
        
        $success_message = "Database updated successfully! " . count($updates_applied) . " updates applied.";
        
    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->rollBack();
        }
        $error_message = "Database update failed: " . $e->getMessage();
    }
}

// Check current database status
try {
    $db = new Database();
    $conn = $db->connect();
    
    $status = [];
    
    // Check tables
    $tables_to_check = ['embed_servers', 'site_settings', 'user_activity', 'watchlist'];
    foreach ($tables_to_check as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '{$table}'");
        $status['tables'][$table] = $stmt->rowCount() > 0;
    }
    
    // Check columns
    $columns_to_check = [
        'movies' => ['created_at', 'is_featured', 'is_trending'],
        'tv_shows' => ['created_at', 'is_featured', 'is_trending']
    ];
    
    foreach ($columns_to_check as $table => $columns) {
        foreach ($columns as $column) {
            $stmt = $conn->query("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
            $status['columns'][$table][$column] = $stmt->rowCount() > 0;
        }
    }
    
    // Check data
    if ($status['tables']['embed_servers']) {
        $stmt = $conn->query("SELECT COUNT(*) FROM embed_servers");
        $status['data']['embed_servers'] = $stmt->fetchColumn();
    }
    
    if ($status['tables']['site_settings']) {
        $stmt = $conn->query("SELECT COUNT(*) FROM site_settings");
        $status['data']['site_settings'] = $stmt->fetchColumn();
    }
    
} catch (Exception $e) {
    $status = [];
    $check_error = "Failed to check database status: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Updater - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .admin-container {
            max-width: 1000px;
            margin: 100px auto 20px;
            padding: 0 20px;
        }
        
        .updater-section {
            background: var(--secondary-color);
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: var(--dark-bg);
            padding: 20px;
            border-radius: 6px;
        }
        
        .status-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-primary);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            color: var(--text-secondary);
        }
        
        .status-value {
            font-weight: 500;
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-missing {
            color: #dc3545;
        }
        
        .status-warning {
            color: #ffc107;
        }
        
        .update-results {
            margin-bottom: 20px;
        }
        
        .update-item {
            padding: 10px 15px;
            margin-bottom: 5px;
            border-radius: 4px;
            background: var(--dark-bg);
            border-left: 4px solid #28a745;
        }
        
        .error-item {
            border-left-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }
        
        .warning-box {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            color: #ffc107;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .info-box {
            background: rgba(23, 162, 184, 0.1);
            border: 1px solid #17a2b8;
            color: #17a2b8;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .update-btn {
            background: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .update-btn:hover {
            background: #c41e3a;
            transform: translateY(-2px);
        }
        
        .update-btn:disabled {
            background: var(--border-color);
            cursor: not-allowed;
            transform: none;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-secondary);
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        @media (max-width: 768px) {
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header scrolled">
        <nav class="navbar">
            <a href="../index.php" class="logo"><?php echo SITE_NAME; ?></a>
            <div class="user-menu">
                <span>Welcome, <?php echo $_SESSION['username']; ?></span>
                <a href="../logout.php" class="btn btn-secondary">Logout</a>
            </div>
        </nav>
    </header>

    <div class="admin-container">
        <div class="admin-header">
            <h1>Database Updater</h1>
            <p>Update your database structure to support all new features</p>
        </div>

        <nav class="admin-nav">
            <a href="index.php">Dashboard</a>
            <a href="movies.php">Movies</a>
            <a href="tv-shows.php">TV Shows</a>
            <a href="users.php">Users</a>
            <a href="servers.php">Servers</a>
            <a href="analytics.php">Analytics</a>
            <a href="import.php">Import</a>
            <a href="maintenance.php">Maintenance</a>
            <a href="database-updater.php" class="active">DB Updater</a>
            <a href="settings.php">Settings</a>
        </nav>

        <?php if (isset($success_message)): ?>
            <div class="success-message"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="error-message"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <?php if (isset($check_error)): ?>
            <div class="error-message"><?php echo $check_error; ?></div>
        <?php endif; ?>

        <!-- Update Results -->
        <?php if (!empty($updates_applied)): ?>
            <div class="updater-section">
                <h2>✅ Updates Applied Successfully</h2>
                <div class="update-results">
                    <?php foreach ($updates_applied as $update): ?>
                        <div class="update-item"><?php echo htmlspecialchars($update); ?></div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="updater-section">
                <h2>⚠️ Update Errors</h2>
                <div class="update-results">
                    <?php foreach ($errors as $error): ?>
                        <div class="update-item error-item"><?php echo htmlspecialchars($error); ?></div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Database Status -->
        <?php if (!empty($status)): ?>
            <div class="updater-section">
                <h2>📊 Current Database Status</h2>
                
                <div class="status-grid">
                    <!-- Tables Status -->
                    <div class="status-card">
                        <h3 class="status-title">Tables</h3>
                        <?php foreach ($status['tables'] ?? [] as $table => $exists): ?>
                            <div class="status-item">
                                <span class="status-label"><?php echo $table; ?></span>
                                <span class="status-value <?php echo $exists ? 'status-ok' : 'status-missing'; ?>">
                                    <?php echo $exists ? '✓ Exists' : '✗ Missing'; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Columns Status -->
                    <div class="status-card">
                        <h3 class="status-title">Table Columns</h3>
                        <?php foreach ($status['columns'] ?? [] as $table => $columns): ?>
                            <?php foreach ($columns as $column => $exists): ?>
                                <div class="status-item">
                                    <span class="status-label"><?php echo $table; ?>.<?php echo $column; ?></span>
                                    <span class="status-value <?php echo $exists ? 'status-ok' : 'status-missing'; ?>">
                                        <?php echo $exists ? '✓' : '✗'; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        <?php endforeach; ?>
                    </div>

                    <!-- Data Status -->
                    <div class="status-card">
                        <h3 class="status-title">Data</h3>
                        <?php foreach ($status['data'] ?? [] as $table => $count): ?>
                            <div class="status-item">
                                <span class="status-label"><?php echo $table; ?> records</span>
                                <span class="status-value <?php echo $count > 0 ? 'status-ok' : 'status-warning'; ?>">
                                    <?php echo number_format($count); ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Update Information -->
        <div class="updater-section">
            <h2>🚀 What This Update Includes</h2>
            
            <div class="info-box">
                <h4>📋 Database Structure Updates:</h4>
                <ul class="feature-list">
                    <li>Embed Servers Management Table</li>
                    <li>Site Settings Configuration Table</li>
                    <li>User Activity Tracking Table</li>
                    <li>Watchlist Functionality Table</li>
                    <li>Performance Indexes for Better Speed</li>
                    <li>Featured & Trending Content Columns</li>
                    <li>Server Testing & Analytics Columns</li>
                </ul>
            </div>

            <div class="info-box">
                <h4>⚙️ Default Data Insertion:</h4>
                <ul class="feature-list">
                    <li>6 Pre-configured Embed Servers (including LetsEmbed)</li>
                    <li>10 Essential Site Settings</li>
                    <li>Security Configurations</li>
                    <li>Cache & Backup Directory Setup</li>
                </ul>
            </div>

            <div class="warning-box">
                <h4>⚠️ Important Notes:</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>This update is safe and uses transactions</li>
                    <li>Existing data will not be lost</li>
                    <li>Backup is recommended before updating</li>
                    <li>Update can be run multiple times safely</li>
                </ul>
            </div>

            <form method="POST" onsubmit="return confirm('Are you sure you want to update the database? This is safe but recommended to backup first.')">
                <input type="hidden" name="action" value="update_database">
                <button type="submit" class="update-btn">
                    🔄 Update Database Structure
                </button>
            </form>
        </div>
    </div>

    <script>
        // Auto-refresh status after update
        if (window.location.search.includes('updated=1')) {
            setTimeout(() => {
                window.location.href = window.location.pathname;
            }, 3000);
        }
    </script>
</body>
</html>
