import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/device_utils.dart';
import '../../../core/services/storage_service.dart';
import '../providers/search_provider.dart';
import '../widgets/search_bar.dart';
import '../widgets/search_result_card.dart';
import '../widgets/recent_searches.dart';
import '../widgets/trending_searches.dart';
import '../../player/screens/player_screen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen>
    with AutomaticKeepAliveClientMixin {
  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;
  late ScrollController _scrollController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchFocusNode = FocusNode();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SearchProvider>().loadInitialData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      context.read<SearchProvider>().loadMoreResults();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    return Scaffold(
      backgroundColor: AppTheme.darkBg,
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar
            _buildSearchBar(),
            
            // Search Content
            Expanded(
              child: Consumer<SearchProvider>(
                builder: (context, provider, child) {
                  if (provider.query.isEmpty) {
                    return _buildSearchHome(provider);
                  } else {
                    return _buildSearchResults(provider);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.cardBg,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppTheme.borderColor),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                decoration: const InputDecoration(
                  hintText: 'Search movies, TV shows...',
                  prefixIcon: Icon(Icons.search, color: AppTheme.textMuted),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onChanged: _onSearchChanged,
                onSubmitted: _onSearchSubmitted,
              ),
            ),
          ),
          if (_searchController.text.isNotEmpty) ...[
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearSearch,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchHome(SearchProvider provider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches
          if (provider.recentSearches.isNotEmpty) ...[
            Text(
              'Recent Searches',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            RecentSearches(
              searches: provider.recentSearches,
              onSearchTap: _onRecentSearchTap,
              onClearAll: () => provider.clearRecentSearches(),
            ),
            const SizedBox(height: 32),
          ],
          
          // Trending Searches
          Text(
            'Trending Now',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          TrendingSearches(
            searches: provider.trendingSearches,
            onSearchTap: _onTrendingSearchTap,
          ),
          
          const SizedBox(height: 32),
          
          // Popular Genres
          Text(
            'Browse by Genre',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildGenreGrid(provider),
        ],
      ),
    );
  }

  Widget _buildGenreGrid(SearchProvider provider) {
    final genres = [
      {'name': 'Action', 'icon': Icons.local_fire_department, 'color': Colors.red},
      {'name': 'Comedy', 'icon': Icons.sentiment_very_satisfied, 'color': Colors.orange},
      {'name': 'Drama', 'icon': Icons.theater_comedy, 'color': Colors.blue},
      {'name': 'Horror', 'icon': Icons.psychology, 'color': Colors.purple},
      {'name': 'Sci-Fi', 'icon': Icons.rocket_launch, 'color': Colors.cyan},
      {'name': 'Romance', 'icon': Icons.favorite, 'color': Colors.pink},
      {'name': 'Thriller', 'icon': Icons.flash_on, 'color': Colors.amber},
      {'name': 'Anime', 'icon': Icons.animation, 'color': Colors.deepOrange},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: genres.length,
      itemBuilder: (context, index) {
        final genre = genres[index];
        return GestureDetector(
          onTap: () => _searchByGenre(genre['name'] as String),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  (genre['color'] as Color).withOpacity(0.8),
                  (genre['color'] as Color).withOpacity(0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  genre['icon'] as IconData,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  genre['name'] as String,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchResults(SearchProvider provider) {
    if (provider.isLoading && provider.searchResults.isEmpty) {
      return _buildLoadingState();
    }

    if (provider.hasError && provider.searchResults.isEmpty) {
      return _buildErrorState(provider);
    }

    if (provider.searchResults.isEmpty) {
      return _buildEmptyState();
    }

    final crossAxisCount = DeviceUtils.getGridCrossAxisCount(context);
    
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverToBoxAdapter(
            child: Text(
              'Found ${provider.totalResults} results for "${provider.query}"',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverMasonryGrid.count(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            childCount: provider.searchResults.length + (provider.isLoadingMore ? 2 : 0),
            itemBuilder: (context, index) {
              if (index >= provider.searchResults.length) {
                return _buildLoadingCard();
              }
              
              final result = provider.searchResults[index];
              return SearchResultCard(
                content: result,
                onTap: () => _navigateToPlayer(result),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppTheme.primaryColor),
          SizedBox(height: 16),
          Text('Searching...'),
        ],
      ),
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: AppTheme.cardBg,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
        ),
      ),
    );
  }

  Widget _buildErrorState(SearchProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'Search Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              provider.errorMessage ?? 'Something went wrong',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => provider.search(provider.query),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.textMuted,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Try different keywords or check your spelling',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _onSearchChanged(String query) {
    // Debounce search
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == query && query.isNotEmpty) {
        context.read<SearchProvider>().search(query);
      }
    });
  }

  void _onSearchSubmitted(String query) {
    if (query.isNotEmpty) {
      context.read<SearchProvider>().search(query);
      _searchFocusNode.unfocus();
    }
  }

  void _onRecentSearchTap(String query) {
    _searchController.text = query;
    context.read<SearchProvider>().search(query);
  }

  void _onTrendingSearchTap(String query) {
    _searchController.text = query;
    context.read<SearchProvider>().search(query);
  }

  void _searchByGenre(String genre) {
    _searchController.text = genre;
    context.read<SearchProvider>().searchByGenre(genre);
  }

  void _clearSearch() {
    _searchController.clear();
    context.read<SearchProvider>().clearSearch();
    _searchFocusNode.unfocus();
  }

  void _navigateToPlayer(Map<String, dynamic> content) {
    final contentType = content['media_type'] ?? 
        (content.containsKey('title') ? 'movie' : 'tv_show');
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlayerScreen(
          tmdbId: content['tmdb_id'] ?? content['id'],
          contentType: contentType,
          title: content['title'] ?? content['name'],
        ),
      ),
    );
  }
}
