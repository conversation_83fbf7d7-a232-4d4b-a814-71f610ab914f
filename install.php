<?php
// StreamFlix Installation Script

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'streamflix';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    $admin_username = $_POST['admin_username'] ?? 'admin';
    $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
    $admin_password = $_POST['admin_password'] ?? 'admin123';
    $tmdb_api_key = $_POST['tmdb_api_key'] ?? '';
    $site_name = $_POST['site_name'] ?? 'StreamFlix';
    $site_url = $_POST['site_url'] ?? 'http://localhost/streamflix';
    
    $errors = [];
    
    // Validate inputs
    if (empty($db_host) || empty($db_name) || empty($db_user)) {
        $errors[] = 'Database credentials are required';
    }
    
    if (empty($admin_username) || empty($admin_email) || empty($admin_password)) {
        $errors[] = 'Admin credentials are required';
    }
    
    if (empty($tmdb_api_key)) {
        $errors[] = 'TMDB API key is required';
    }
    
    if (empty($errors)) {
        try {
            // Test database connection
            $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}`");
            $pdo->exec("USE `{$db_name}`");
            
            // Read and execute SQL file
            $sql = file_get_contents('config/install.sql');
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
            
            // Update admin user
            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET username = ?, email = ?, password = ? WHERE role = 'admin'");
            $stmt->execute([$admin_username, $admin_email, $hashed_password]);
            
            // Update configuration file
            $config_content = "<?php
// Database Configuration
class Database {
    private \$host = '{$db_host}';
    private \$db_name = '{$db_name}';
    private \$username = '{$db_user}';
    private \$password = '{$db_pass}';
    private \$conn;

    public function connect() {
        \$this->conn = null;
        try {
            \$this->conn = new PDO(
                \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name,
                \$this->username,
                \$this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException \$e) {
            echo \"Connection Error: \" . \$e->getMessage();
        }
        return \$this->conn;
    }
}

// TMDB API Configuration
define('TMDB_API_KEY', '{$tmdb_api_key}');
define('TMDB_BASE_URL', 'https://api.themoviedb.org/3');
define('TMDB_IMAGE_BASE', 'https://image.tmdb.org/t/p/');

// Embed Servers Configuration
\$embed_servers = [
    'autoembed' => [
        'name' => 'AutoEmbed',
        'movie_url' => 'https://player.autoembed.cc/embed/movie/{id}',
        'tv_url' => 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}',
        'priority' => 1
    ],
    'vidjoy' => [
        'name' => 'VidJoy',
        'movie_url' => 'https://vidjoy.pro/embed/movie/{id}',
        'tv_url' => 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}',
        'priority' => 2
    ],
    'vidzee' => [
        'name' => 'VidZee',
        'movie_url' => 'https://player.vidzee.wtf/embed/movie/{id}',
        'tv_url' => 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}',
        'priority' => 3
    ]
];

// Site Configuration
define('SITE_NAME', '{$site_name}');
define('SITE_URL', '{$site_url}');
define('ADMIN_EMAIL', '{$admin_email}');

// Security
define('JWT_SECRET', '" . bin2hex(random_bytes(32)) . "');
define('ENCRYPTION_KEY', '" . bin2hex(random_bytes(32)) . "');

session_start();
?>";
            
            file_put_contents('config/database.php', $config_content);
            
            $success = true;
            
        } catch (Exception $e) {
            $errors[] = 'Installation failed: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StreamFlix Installation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #141414 0%, #000000 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: #221f1f;
            padding: 40px;
            border-radius: 8px;
            width: 100%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .install-header h1 {
            color: #e50914;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            background: #141414;
            border: 1px solid #333333;
            border-radius: 4px;
            color: #ffffff;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #e50914;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .btn {
            background: #e50914;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #f40612;
        }
        
        .error {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .success {
            background: #28a745;
            color: white;
            padding: 20px;
            border-radius: 4px;
            text-align: center;
        }
        
        .success h2 {
            margin-bottom: 10px;
        }
        
        .success a {
            color: white;
            text-decoration: underline;
        }
        
        .help-text {
            font-size: 0.9rem;
            color: #b3b3b3;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .install-container {
                margin: 20px;
                padding: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="install-container">
        <?php if (isset($success) && $success): ?>
            <div class="success">
                <h2>🎉 Installation Successful!</h2>
                <p>StreamFlix has been installed successfully.</p>
                <p style="margin: 20px 0;">
                    <strong>Admin Login:</strong><br>
                    Username: <?php echo htmlspecialchars($admin_username); ?><br>
                    Password: <?php echo htmlspecialchars($admin_password); ?>
                </p>
                <p>
                    <a href="index.php">Go to Homepage</a> | 
                    <a href="admin/">Admin Panel</a>
                </p>
                <p style="margin-top: 20px; font-size: 0.9rem;">
                    <strong>Important:</strong> Delete this install.php file for security.
                </p>
            </div>
        <?php else: ?>
            <div class="install-header">
                <h1>StreamFlix</h1>
                <p>Installation Setup</p>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="error">
                    <?php foreach ($errors as $error): ?>
                        <p><?php echo htmlspecialchars($error); ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <h3 style="margin-bottom: 20px; color: #e50914;">Database Configuration</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="db_host">Database Host</label>
                        <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($db_host ?? 'localhost'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name">Database Name</label>
                        <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($db_name ?? 'streamflix'); ?>" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="db_user">Database Username</label>
                        <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($db_user ?? 'root'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass">Database Password</label>
                        <input type="password" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($db_pass ?? ''); ?>">
                    </div>
                </div>
                
                <h3 style="margin: 30px 0 20px; color: #e50914;">Admin Account</h3>
                
                <div class="form-group">
                    <label for="admin_username">Admin Username</label>
                    <input type="text" id="admin_username" name="admin_username" value="<?php echo htmlspecialchars($admin_username ?? 'admin'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_email">Admin Email</label>
                    <input type="email" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($admin_email ?? '<EMAIL>'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="admin_password">Admin Password</label>
                    <input type="password" id="admin_password" name="admin_password" value="<?php echo htmlspecialchars($admin_password ?? 'admin123'); ?>" required>
                </div>
                
                <h3 style="margin: 30px 0 20px; color: #e50914;">Site Configuration</h3>
                
                <div class="form-group">
                    <label for="tmdb_api_key">TMDB API Key</label>
                    <input type="text" id="tmdb_api_key" name="tmdb_api_key" value="<?php echo htmlspecialchars($tmdb_api_key ?? ''); ?>" required>
                    <div class="help-text">Get your API key from <a href="https://www.themoviedb.org/settings/api" target="_blank" style="color: #e50914;">TMDB</a></div>
                </div>
                
                <div class="form-group">
                    <label for="site_name">Site Name</label>
                    <input type="text" id="site_name" name="site_name" value="<?php echo htmlspecialchars($site_name ?? 'StreamFlix'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="site_url">Site URL</label>
                    <input type="url" id="site_url" name="site_url" value="<?php echo htmlspecialchars($site_url ?? 'http://localhost/streamflix'); ?>" required>
                    <div class="help-text">Full URL to your site (without trailing slash)</div>
                </div>
                
                <button type="submit" class="btn">Install StreamFlix</button>
            </form>
        <?php endif; ?>
    </div>
</body>
</html>
