import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/image_utils.dart';

class HeroSection extends StatelessWidget {
  final Map<String, dynamic> content;
  final VoidCallback onPlayTap;
  final VoidCallback onInfoTap;

  const HeroSection({
    super.key,
    required this.content,
    required this.onPlayTap,
    required this.onInfoTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final heroHeight = screenHeight * 0.6;
    
    return Container(
      height: heroHeight,
      child: Stack(
        children: [
          // Background Image
          _buildBackgroundImage(),
          
          // Gradient Overlay
          _buildGradientOverlay(),
          
          // Content
          _buildContent(context),
          
          // Play Button Overlay
          _buildPlayButtonOverlay(),
        ],
      ),
    );
  }

  Widget _buildBackgroundImage() {
    final backdropPath = content['backdrop_path'] ?? content['poster_path'];
    
    return Positioned.fill(
      child: CachedNetworkImage(
        imageUrl: ImageUtils.getImageUrl(backdropPath, ImageSize.original),
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: AppTheme.cardBg,
          child: const Center(
            child: CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: AppTheme.cardBg,
          child: const Icon(
            Icons.image_not_supported,
            color: AppTheme.textMuted,
            size: 48,
          ),
        ),
      ),
    );
  }

  Widget _buildGradientOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black26,
              Colors.black54,
              Colors.black87,
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Positioned(
      left: 16,
      right: 16,
      bottom: 80,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          Text(
            content['title'] ?? content['name'] ?? 'Unknown Title',
            style: Theme.of(context).textTheme.displayMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w800,
              shadows: [
                const Shadow(
                  offset: Offset(0, 2),
                  blurRadius: 4,
                  color: Colors.black54,
                ),
              ],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // Metadata
          _buildMetadata(context),
          
          const SizedBox(height: 12),
          
          // Overview
          if (content['overview'] != null && content['overview'].isNotEmpty)
            Text(
              content['overview'],
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.9),
                height: 1.4,
                shadows: [
                  const Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 2,
                    color: Colors.black54,
                  ),
                ],
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          
          const SizedBox(height: 24),
          
          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildMetadata(BuildContext context) {
    final year = _getYear();
    final rating = _getRating();
    final contentType = _getContentType();
    
    return Row(
      children: [
        // Content Type
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            contentType,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        
        if (year != null) ...[
          const SizedBox(width: 12),
          Text(
            year,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
        
        if (rating != null) ...[
          const SizedBox(width: 12),
          Row(
            children: [
              const Icon(
                Icons.star,
                color: Colors.amber,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                rating,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withOpacity(0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // Play Button
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: onPlayTap,
            icon: const Icon(Icons.play_arrow, size: 24),
            label: const Text(
              'Play',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // More Info Button
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onInfoTap,
            icon: const Icon(Icons.info_outline, size: 20),
            label: const Text(
              'Info',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white, width: 1.5),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlayButtonOverlay() {
    return Positioned.fill(
      child: Center(
        child: GestureDetector(
          onTap: onPlayTap,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white.withOpacity(0.8),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),
      ),
    );
  }

  String? _getYear() {
    final releaseDate = content['release_date'] ?? content['first_air_date'];
    if (releaseDate != null && releaseDate.isNotEmpty) {
      try {
        final date = DateTime.parse(releaseDate);
        return date.year.toString();
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  String? _getRating() {
    final voteAverage = content['vote_average'];
    if (voteAverage != null && voteAverage > 0) {
      return voteAverage.toStringAsFixed(1);
    }
    return null;
  }

  String _getContentType() {
    if (content.containsKey('title')) {
      return 'MOVIE';
    } else if (content.containsKey('name')) {
      return 'TV SHOW';
    }
    return 'CONTENT';
  }
}

// Image Utils Helper
class ImageUtils {
  static const String tmdbBaseUrl = 'https://image.tmdb.org/t/p/';
  
  static String getImageUrl(String? path, ImageSize size) {
    if (path == null || path.isEmpty) {
      return 'https://via.placeholder.com/500x750/1f1f1f/666666?text=No+Image';
    }
    
    return '$tmdbBaseUrl${size.value}$path';
  }
}

enum ImageSize {
  w200('w200'),
  w300('w300'),
  w500('w500'),
  w780('w780'),
  w1280('w1280'),
  original('original');
  
  const ImageSize(this.value);
  final String value;
}
