/* StreamFlix - Netflix Style CSS */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #e50914;
    --secondary-color: #221f1f;
    --dark-bg: #141414;
    --darker-bg: #000000;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --hover-color: #f40612;
    --border-color: #333333;
    --gradient-bg: linear-gradient(180deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.7) 100%);
    --netflix-shadow: 0 4px 8px rgba(0,0,0,0.45);
    --netflix-hover-shadow: 0 8px 25px rgba(0,0,0,0.5);
}

body {
    font-family: 'Netflix Sans', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.4;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Header Styles - Netflix Design */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.7) 0%, transparent 100%);
    z-index: 1000;
    transition: all 0.4s ease;
    padding: 0 4%;
    backdrop-filter: blur(10px);
}

.header.scrolled {
    background-color: rgba(20, 20, 20, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 1px 5px rgba(0,0,0,0.25);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 0;
    max-width: 1920px;
    margin: 0 auto;
}

.logo {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--primary-color);
    text-decoration: none;
    letter-spacing: -2px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 35px;
    margin-left: 50px;
}

.nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 400;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 8px 0;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--text-primary);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 25px;
}

.search-box {
    position: relative;
}

.search-input {
    background: rgba(0,0,0,0.6);
    border: 1px solid rgba(255,255,255,0.15);
    color: var(--text-primary);
    padding: 10px 45px 10px 15px;
    border-radius: 6px;
    width: 280px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(0,0,0,0.8);
    box-shadow: 0 0 0 2px rgba(229, 9, 20, 0.2);
}

.search-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.search-btn:hover {
    color: var(--text-primary);
}

/* Mobile Menu */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
}

.user-dropdown {
    position: relative;
}

.user-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.user-btn:hover {
    background: rgba(255,255,255,0.1);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--secondary-color);
    border-radius: 6px;
    padding: 10px 0;
    min-width: 150px;
    box-shadow: var(--netflix-shadow);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.user-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    display: block;
    padding: 10px 20px;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: background 0.3s ease;
}

.dropdown-menu a:hover {
    background: rgba(255,255,255,0.1);
}

/* Hero Section - Netflix Style */
.hero {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 0;
    background-attachment: fixed;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to right,
        rgba(0,0,0,0.8) 0%,
        rgba(0,0,0,0.4) 50%,
        rgba(0,0,0,0.8) 100%
    ),
    linear-gradient(
        to top,
        rgba(0,0,0,0.8) 0%,
        rgba(0,0,0,0.2) 50%,
        rgba(0,0,0,0.8) 100%
    );
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 650px;
    padding: 0 4%;
    margin-left: 0;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 2px 4px 8px rgba(0,0,0,0.8);
    line-height: 1.1;
    letter-spacing: -1px;
}

.hero-description {
    font-size: 1.4rem;
    color: var(--text-primary);
    margin-bottom: 35px;
    line-height: 1.4;
    text-shadow: 1px 2px 4px rgba(0,0,0,0.8);
    font-weight: 400;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 35px;
    border: none;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--netflix-shadow);
}

.btn-primary {
    background-color: var(--text-primary);
    color: var(--darker-bg);
    border: 2px solid var(--text-primary);
}

.btn-primary:hover {
    background-color: rgba(255,255,255,0.8);
    transform: translateY(-2px);
    box-shadow: var(--netflix-hover-shadow);
}

.btn-secondary {
    background-color: rgba(109,109,110,0.7);
    color: var(--text-primary);
    border: 2px solid rgba(109,109,110,0.7);
}

.btn-secondary:hover {
    background-color: rgba(109,109,110,0.9);
    transform: translateY(-2px);
    box-shadow: var(--netflix-hover-shadow);
}

.hero-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 1rem;
    color: var(--text-primary);
}

.hero-rating {
    display: flex;
    align-items: center;
    gap: 5px;
}

.hero-year,
.hero-runtime {
    color: var(--text-secondary);
}

/* Content Sections - Netflix Style */
.content-section {
    padding: 0 4%;
    margin-bottom: 60px;
    position: relative;
}

.section-title {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
    padding-left: 0;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(185px, 1fr));
    gap: 8px;
    margin-bottom: 30px;
}

.content-card {
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    background: var(--secondary-color);
    aspect-ratio: 2/3;
}

.content-card:hover {
    transform: scale(1.08);
    z-index: 10;
    box-shadow: 0 10px 30px rgba(0,0,0,0.6);
}

.content-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
}

.content-card:hover img {
    opacity: 0.8;
    filter: brightness(1.1);
}

.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.9));
    padding: 15px 12px 12px;
    transform: translateY(100%);
    transition: transform 0.4s ease;
}

.content-card:hover .card-overlay {
    transform: translateY(0);
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
    line-height: 1.2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-info {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rating {
    display: flex;
    align-items: center;
    gap: 3px;
}

.star {
    color: #ffd700;
    font-size: 0.7rem;
}

/* Horizontal Scroll - Netflix Style */
.content-row {
    position: relative;
    margin-bottom: 50px;
    overflow: hidden;
}

.content-slider {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 10px 0 20px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-snap-type: x mandatory;
}

.content-slider::-webkit-scrollbar {
    display: none;
}

.slider-card {
    min-width: 185px;
    flex-shrink: 0;
    scroll-snap-align: start;
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.7);
    border: none;
    color: var(--text-primary);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    z-index: 5;
    transition: all 0.3s ease;
    opacity: 0;
}

.content-row:hover .slider-nav {
    opacity: 1;
}

.slider-nav:hover {
    background: rgba(0,0,0,0.9);
    transform: translateY(-50%) scale(1.1);
}

.slider-nav.prev {
    left: 10px;
}

.slider-nav.next {
    right: 10px;
}

/* Player Styles */
.player-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    background: var(--darker-bg);
    border-radius: 8px;
    overflow: hidden;
}

.player-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.player-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.server-selector {
    padding: 20px;
    background: var(--secondary-color);
    border-top: 1px solid var(--border-color);
}

.server-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.server-btn {
    padding: 8px 16px;
    background: var(--border-color);
    color: var(--text-primary);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.server-btn:hover,
.server-btn.active {
    background: var(--primary-color);
}

/* Modal Styles - Netflix Style */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.85);
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: var(--secondary-color);
    margin: 3% auto;
    padding: 40px;
    border-radius: 12px;
    width: 95%;
    max-width: 900px;
    position: relative;
    box-shadow: 0 20px 60px rgba(0,0,0,0.5);
    animation: slideIn 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    position: absolute;
    right: 25px;
    top: 20px;
    color: var(--text-secondary);
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(0,0,0,0.3);
}

.close:hover {
    color: var(--text-primary);
    background: rgba(0,0,0,0.6);
    transform: scale(1.1);
}

/* Content Details */
.content-details {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.content-poster img {
    width: 300px;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.content-info {
    flex: 1;
}

.content-info h2 {
    font-size: 2.2rem;
    margin-bottom: 15px;
    color: var(--text-primary);
    font-weight: 700;
}

.content-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    color: var(--text-secondary);
    font-size: 1rem;
}

.content-meta .rating {
    color: #ffd700;
}

.overview {
    line-height: 1.6;
    margin-bottom: 25px;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

/* Season Selector */
.season-selector {
    margin-top: 20px;
}

.season-selector label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 600;
}

.season-selector select {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 1rem;
    min-width: 200px;
}

/* Mobile Modal */
@media (max-width: 768px) {
    .modal-content {
        margin: 5% auto;
        padding: 25px;
        width: 95%;
        max-height: 85vh;
    }

    .content-details {
        flex-direction: column;
        gap: 20px;
    }

    .content-poster {
        align-self: center;
    }

    .content-poster img {
        width: 250px;
    }

    .content-info h2 {
        font-size: 1.8rem;
        text-align: center;
    }

    .content-meta {
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .btn {
        text-align: center;
        justify-content: center;
    }
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Mobile App-like Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 6px;
    }

    .slider-card {
        min-width: 160px;
    }
}

@media (max-width: 768px) {
    /* Mobile Header */
    .navbar {
        padding: 12px 0;
        position: relative;
    }

    .logo {
        font-size: 2rem;
    }

    .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: rgba(20, 20, 20, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        padding: 20px;
        gap: 20px;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .mobile-menu-btn {
        display: block;
    }

    .search-input {
        width: 180px;
        padding: 8px 35px 8px 12px;
    }

    /* Mobile Hero */
    .hero {
        height: 70vh;
        background-attachment: scroll;
    }

    .hero-content {
        max-width: 100%;
        padding: 0 20px;
    }

    .hero-title {
        font-size: 2.8rem;
        line-height: 1.1;
        margin-bottom: 15px;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 25px;
        line-height: 1.3;
    }

    .hero-buttons {
        flex-direction: row;
        gap: 15px;
    }

    .btn {
        padding: 12px 25px;
        font-size: 0.95rem;
    }

    /* Mobile Content */
    .content-section {
        padding: 0 20px;
        margin-bottom: 40px;
    }

    .section-title {
        font-size: 1.4rem;
        margin-bottom: 12px;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 8px;
    }

    .slider-card {
        min-width: 140px;
    }

    .content-slider {
        gap: 8px;
        padding: 8px 0 15px 0;
    }

    /* Hide slider navigation on mobile */
    .slider-nav {
        display: none;
    }

    /* Mobile Cards */
    .content-card {
        border-radius: 4px;
    }

    .card-title {
        font-size: 0.8rem;
    }

    .card-info {
        font-size: 0.75rem;
    }

    .star {
        font-size: 0.65rem;
    }
}

@media (max-width: 480px) {
    /* Extra Small Mobile */
    .header {
        padding: 0 15px;
    }

    .logo {
        font-size: 1.8rem;
    }

    .search-input {
        width: 140px;
        font-size: 0.85rem;
    }

    .user-menu {
        gap: 15px;
    }

    /* Hero for small screens */
    .hero {
        height: 60vh;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .btn {
        text-align: center;
        justify-content: center;
        padding: 14px 20px;
    }

    /* Content for small screens */
    .content-section {
        padding: 0 15px;
        margin-bottom: 35px;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 6px;
    }

    .slider-card {
        min-width: 120px;
    }

    .content-slider {
        gap: 6px;
    }

    /* Touch-friendly interactions */
    .content-card {
        transition: transform 0.2s ease;
    }

    .content-card:active {
        transform: scale(0.95);
    }
}

/* App-like Features */
@media (max-width: 768px) {
    /* iOS Safari viewport fix */
    .hero {
        min-height: -webkit-fill-available;
    }

    /* Touch scrolling */
    .content-slider {
        -webkit-overflow-scrolling: touch;
        scroll-snap-type: x mandatory;
    }

    .slider-card {
        scroll-snap-align: start;
    }

    /* Prevent zoom on input focus */
    .search-input {
        font-size: 16px;
    }

    /* App-like status bar */
    body {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
    }

    .header {
        padding-top: max(15px, env(safe-area-inset-top));
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.hidden { display: none; }
.visible { display: block; }
.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.gap-10 { gap: 10px; }
.gap-20 { gap: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
