<?php
require_once '../includes/functions.php';

// Check if user is admin
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

header('Content-Type: application/json');

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';
$type = $input['type'] ?? 'movie';

$streamflix = new StreamFlix();
$imported_count = 0;

try {
    switch ($action) {
        case 'import_trending':
        case 'import_trending_movies':
        case 'import_trending_tv':
            $endpoint = $type === 'movie' ? '/trending/movie/week' : '/trending/tv/week';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    if ($type === 'movie') {
                        $result = $streamflix->importMovie($item['id']);
                    } else {
                        $result = $streamflix->importTVShow($item['id']);
                    }

                    if ($result) {
                        $imported_count++;
                        // Mark as trending
                        $streamflix->markAsTrending($result, $type);
                    }

                    // Small delay to avoid rate limiting
                    usleep(100000); // 0.1 second
                }
            }
            break;

        case 'import_popular':
        case 'import_popular_movies':
        case 'import_popular_tv':
            $endpoint = $type === 'movie' ? '/movie/popular' : '/tv/popular';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    if ($type === 'movie') {
                        $result = $streamflix->importMovie($item['id']);
                    } else {
                        $result = $streamflix->importTVShow($item['id']);
                    }

                    if ($result) {
                        $imported_count++;
                        // Mark as featured
                        $streamflix->markAsFeatured($result, $type);
                    }

                    // Small delay to avoid rate limiting
                    usleep(100000); // 0.1 second
                }
            }
            break;

        case 'import_now_playing':
            $endpoint = '/movie/now_playing';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importMovie($item['id']);
                    if ($result) {
                        $imported_count++;
                        $streamflix->markAsFeatured($result, 'movie');
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_upcoming':
            $endpoint = '/movie/upcoming';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importMovie($item['id']);
                    if ($result) {
                        $imported_count++;
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_airing_today':
            $endpoint = '/tv/airing_today';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importTVShow($item['id']);
                    if ($result) {
                        $imported_count++;
                        $streamflix->markAsTrending($result, 'tv_show');
                    }
                    usleep(100000);
                }
            }
            break;

        case 'import_on_the_air':
            $endpoint = '/tv/on_the_air';
            $data = $streamflix->fetchFromTMDB($endpoint);

            if ($data && isset($data['results'])) {
                foreach ($data['results'] as $item) {
                    $result = $streamflix->importTVShow($item['id']);
                    if ($result) {
                        $imported_count++;
                        $streamflix->markAsFeatured($result, 'tv_show');
                    }
                    usleep(100000);
                }
            }
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }
    
    echo json_encode([
        'success' => true,
        'count' => $imported_count,
        'message' => "Successfully imported {$imported_count} items"
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
