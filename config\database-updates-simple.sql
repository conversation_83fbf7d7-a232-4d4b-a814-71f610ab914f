-- StreamFlix Database Updates (MySQL Compatible)
-- Version: 2.0.0

-- Create embed_servers table
CREATE TABLE IF NOT EXISTS embed_servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    movie_url TEXT NOT NULL,
    tv_url TEXT NOT NULL,
    priority INT DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    last_tested TIMESTAMP NULL,
    last_response_time INT NULL,
    last_status_code INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create site_settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create user_activity table
CREATE TABLE IF NOT EXISTS user_activity (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    activity_type ENUM('login', 'logout', 'view_movie', 'view_tv_show', 'search') NOT NULL,
    content_type ENUM('movie', 'tv_show') NULL,
    content_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create watchlist table
CREATE TABLE IF NOT EXISTS watchlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    content_type ENUM('movie', 'tv_show') NOT NULL,
    content_id INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default embed servers
INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('AutoEmbed', 'https://player.autoembed.cc/embed/movie/{id}', 'https://player.autoembed.cc/embed/tv/{id}/{season}/{episode}', 1, 1);

INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('VidJoy', 'https://vidjoy.pro/embed/movie/{id}', 'https://vidjoy.pro/embed/tv/{id}/{season}/{episode}', 2, 1);

INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('VidZee', 'https://player.vidzee.wtf/embed/movie/{id}', 'https://player.vidzee.wtf/embed/tv/{id}/{season}/{episode}', 3, 1);

INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('SuperEmbed', 'https://multiembed.mov/directstream.php?video_id={id}', 'https://multiembed.mov/directstream.php?video_id={id}&s={season}&e={episode}', 4, 1);

INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('EmbedSu', 'https://embed.su/embed/movie/{id}', 'https://embed.su/embed/tv/{id}/{season}/{episode}', 5, 1);

INSERT IGNORE INTO embed_servers (name, movie_url, tv_url, priority, is_active) VALUES
('LetsEmbed', 'https://letsembed.cc/embed/movie/?id={id}', 'https://letsembed.cc/embed/tv/?id={id}/{season}/{episode}', 6, 1);

-- Insert default site settings
INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'StreamFlix', 'string', 'Website name');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_description', 'Premium Movie & TV Show Streaming Platform', 'string', 'Website description');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('tmdb_api_key', '', 'string', 'TMDB API Key for content import');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('maintenance_mode', '0', 'boolean', 'Enable maintenance mode');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('registration_enabled', '1', 'boolean', 'Allow new user registrations');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('featured_content_limit', '10', 'number', 'Number of featured content items');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('trending_content_limit', '20', 'number', 'Number of trending content items');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('max_servers_per_content', '6', 'number', 'Maximum embed servers per content');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('cache_duration', '3600', 'number', 'Cache duration in seconds');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('auto_import_trending', '0', 'boolean', 'Auto import trending content daily');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('player_autoplay', '1', 'boolean', 'Enable autoplay in player');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('show_ratings', '1', 'boolean', 'Show content ratings');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('enable_comments', '0', 'boolean', 'Enable user comments');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('enable_reviews', '0', 'boolean', 'Enable user reviews');

INSERT IGNORE INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('content_per_page', '24', 'number', 'Content items per page');
